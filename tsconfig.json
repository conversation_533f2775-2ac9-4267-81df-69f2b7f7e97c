{"compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES2020"], "types": ["vitest/globals", "@testing-library/jest-dom"], "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"~/*": ["./src/*"]}, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist", "**/*.stories.*", "src/examples/**/*"]}