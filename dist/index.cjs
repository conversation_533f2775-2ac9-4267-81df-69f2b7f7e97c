"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const f=require("react/jsx-runtime"),S=require("react"),or=require("react-dom");function Io(e){const t=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const n in e)if(n!=="default"){const r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:()=>e[n]})}}return t.default=e,Object.freeze(t)}const m=Io(S),Fo=Io(or);function zr(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function $o(...e){return t=>{let n=!1;const r=e.map(o=>{const s=zr(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():zr(e[o],null)}}}}function je(...e){return m.useCallback($o(...e),e)}function bn(e){const t=ii(e),n=m.forwardRef((r,o)=>{const{children:s,...a}=r,i=m.Children.toArray(s),u=i.find(ci);if(u){const l=u.props.children,d=i.map(c=>c===u?m.Children.count(l)>1?m.Children.only(null):m.isValidElement(l)?l.props.children:null:c);return f.jsx(t,{...a,ref:o,children:m.isValidElement(l)?m.cloneElement(l,void 0,d):null})}return f.jsx(t,{...a,ref:o,children:s})});return n.displayName=`${e}.Slot`,n}var ai=bn("Slot");function ii(e){const t=m.forwardRef((n,r)=>{const{children:o,...s}=n;if(m.isValidElement(o)){const a=di(o),i=ui(s,o.props);return o.type!==m.Fragment&&(i.ref=r?$o(r,a):a),m.cloneElement(o,i)}return m.Children.count(o)>1?m.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var li=Symbol("radix.slottable");function ci(e){return m.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===li}function ui(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...i)=>{const u=s(...i);return o(...i),u}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function di(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Oo(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Oo(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Do(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Oo(e))&&(r&&(r+=" "),r+=t);return r}const Hr=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Gr=Do,Q=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Gr(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,a=Object.keys(o).map(l=>{const d=n==null?void 0:n[l],c=s==null?void 0:s[l];if(d===null)return null;const g=Hr(d)||Hr(c);return o[l][g]}),i=n&&Object.entries(n).reduce((l,d)=>{let[c,g]=d;return g===void 0||(l[c]=g),l},{}),u=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((l,d)=>{let{class:c,className:g,...p}=d;return Object.entries(p).every(b=>{let[h,v]=b;return Array.isArray(v)?v.includes({...s,...i}[h]):{...s,...i}[h]===v})?[...l,c,g]:l},[]);return Gr(e,a,u,n==null?void 0:n.class,n==null?void 0:n.className)};var To=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{d:"M10 6a.75.75 0 0 1 .75.75v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 .75-.75Z"}),S.createElement("path",{d:"M10 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"}),S.createElement("path",{fillRule:"evenodd",d:"M3.5 8.25a4.75 4.75 0 0 1 4.75-4.75h3.5a4.75 4.75 0 0 1 4.75 4.75v2.5a4.75 4.75 0 0 1-4.573 4.747l-1.335 1.714a.75.75 0 0 1-1.189-.006l-1.3-1.707a4.75 4.75 0 0 1-4.603-4.748v-2.5Zm4.75-3.25a3.25 3.25 0 0 0-3.25 3.25v2.5a3.25 3.25 0 0 0 3.25 3.25h.226a.75.75 0 0 1 .597.296l.934 1.225.96-1.232a.75.75 0 0 1 .591-.289h.192a3.25 3.25 0 0 0 3.25-3.25v-2.5a3.25 3.25 0 0 0-3.25-3.25h-3.5Z"}))};To.displayName="AlertBubbleIcon";var Vo=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{fillRule:"evenodd",d:"M15.78 5.97a.75.75 0 0 1 0 1.06l-6.5 6.5a.75.75 0 0 1-1.06 0l-3.25-3.25a.75.75 0 1 1 1.06-1.06l2.72 2.72 5.97-5.97a.75.75 0 0 1 1.06 0Z"}))};Vo.displayName="CheckIcon";var sr=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{fillRule:"evenodd",d:"M5.72 8.47a.75.75 0 0 1 1.06 0l3.47 3.47 3.47-3.47a.75.75 0 1 1 1.06 1.06l-4 4a.75.75 0 0 1-1.06 0l-4-4a.75.75 0 0 1 0-1.06Z"}))};sr.displayName="ChevronDownIcon";var ar=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{fillRule:"evenodd",d:"M11.764 5.204a.75.75 0 0 1 .032 1.06l-3.516 3.736 3.516 3.736a.75.75 0 1 1-1.092 1.028l-4-4.25a.75.75 0 0 1 0-1.028l4-4.25a.75.75 0 0 1 1.06-.032Z"}))};ar.displayName="ChevronLeftIcon";var ir=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{fillRule:"evenodd",d:"M7.72 14.53a.75.75 0 0 1 0-1.06l3.47-3.47-3.47-3.47a.75.75 0 0 1 1.06-1.06l4 4a.75.75 0 0 1 0 1.06l-4 4a.75.75 0 0 1-1.06 0Z"}))};ir.displayName="ChevronRightIcon";var lr=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{fillRule:"evenodd",d:"M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"}))};lr.displayName="ChevronUpIcon";var Lo=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{d:"M3 6a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5h-12.5a.75.75 0 0 1-.75-.75Z"}),S.createElement("path",{d:"M6.75 14a.75.75 0 0 1 .75-.75h5a.75.75 0 0 1 0 1.5h-5a.75.75 0 0 1-.75-.75Z"}),S.createElement("path",{d:"M5.5 9.25a.75.75 0 0 0 0 1.5h9a.75.75 0 0 0 0-1.5h-9Z"}))};Lo.displayName="FilterIcon";var Bo=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{d:"M3.5 9.25a.75.75 0 0 0 1.5 0 3 3 0 0 1 3-3h6.566l-1.123 1.248a.75.75 0 1 0 1.114 1.004l2.25-2.5a.75.75 0 0 0-.027-1.032l-2.25-2.25a.75.75 0 1 0-1.06 1.06l.97.97h-6.44a4.5 4.5 0 0 0-4.5 4.5Z"}),S.createElement("path",{d:"M16.5 10.75a.75.75 0 0 0-1.5 0 3 3 0 0 1-3 3h-6.566l1.123-1.248a.75.75 0 1 0-1.114-1.004l-2.25 2.5a.75.75 0 0 0 .027 1.032l2.25 2.25a.75.75 0 0 0 1.06-1.06l-.97-.97h6.44a4.5 4.5 0 0 0 4.5-4.5Z"}))};Bo.displayName="RefreshIcon";var zo=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{fillRule:"evenodd",d:"M12.323 13.383a5.5 5.5 0 1 1 1.06-1.06l2.897 2.897a.75.75 0 1 1-1.06 1.06l-2.897-2.897Zm.677-4.383a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"}))};zo.displayName="SearchIcon";var Ho=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{fillRule:"evenodd",d:"M12.5 10a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Zm-1.5 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),S.createElement("path",{fillRule:"evenodd",d:"M9.377 2.5c-.926 0-1.676.75-1.676 1.676v.688c0 .056-.043.17-.198.251-.153.08-.303.168-.448.262-.147.097-.268.076-.318.048l-.6-.346a1.676 1.676 0 0 0-2.29.613l-.622 1.08a1.676 1.676 0 0 0 .613 2.289l.648.374c.048.028.124.12.119.29a5.484 5.484 0 0 0 .005.465c.009.175-.07.27-.119.299l-.653.377a1.676 1.676 0 0 0-.613 2.29l.623 1.08a1.676 1.676 0 0 0 2.29.613l.7-.405c.048-.028.166-.048.312.043.115.071.233.139.353.202.155.08.198.195.198.251v.811c0 .926.75 1.676 1.676 1.676h1.246c.926 0 1.676-.75 1.676-1.676v-.81c0-.057.042-.171.197-.252.121-.063.239-.13.354-.202.146-.091.264-.07.312-.043l.7.405a1.676 1.676 0 0 0 2.29-.614l.623-1.08a1.676 1.676 0 0 0-.613-2.289l-.653-.377c-.05-.029-.128-.123-.119-.3a5.494 5.494 0 0 0 .005-.463c-.005-.171.07-.263.12-.291l.647-.374a1.676 1.676 0 0 0 .613-2.29l-.623-1.079a1.676 1.676 0 0 0-2.29-.613l-.6.346c-.049.028-.17.048-.318-.048a5.4 5.4 0 0 0-.448-.262c-.155-.081-.197-.195-.197-.251v-.688c0-.926-.75-1.676-1.676-1.676h-1.246Zm-.176 1.676c0-.097.078-.176.176-.176h1.246c.097 0 .176.079.176.176v.688c0 .728.462 1.298 1.003 1.58.11.058.219.122.323.19.517.337 1.25.458 1.888.09l.6-.346a.176.176 0 0 1 .24.064l.623 1.08a.176.176 0 0 1-.064.24l-.648.374c-.623.36-.888 1.034-.868 1.638a4.184 4.184 0 0 1-.004.337c-.032.615.23 1.31.867 1.677l.653.377a.176.176 0 0 1 .064.24l-.623 1.08a.176.176 0 0 1-.24.065l-.701-.405c-.624-.36-1.341-.251-1.855.069a3.91 3.91 0 0 1-.255.145c-.54.283-1.003.853-1.003 1.581v.811a.176.176 0 0 1-.176.176h-1.246a.176.176 0 0 1-.176-.176v-.81c0-.73-.462-1.3-1.003-1.582a3.873 3.873 0 0 1-.255-.146c-.514-.32-1.23-.428-1.855-.068l-.7.405a.176.176 0 0 1-.241-.065l-.623-1.08a.176.176 0 0 1 .064-.24l.653-.377c.637-.368.899-1.062.867-1.677a3.97 3.97 0 0 1-.004-.337c.02-.604-.245-1.278-.868-1.638l-.648-.374a.176.176 0 0 1-.064-.24l.623-1.08a.176.176 0 0 1 .24-.064l.6.346c.638.368 1.37.247 1.888-.09a3.85 3.85 0 0 1 .323-.19c.54-.282 1.003-.852 1.003-1.58v-.688Z"}))};Ho.displayName="SettingsIcon";var Go=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{d:"M7.75 6.06v7.69a.75.75 0 0 1-1.5 0v-7.69l-1.72 1.72a.75.75 0 0 1-1.06-1.06l3-3a.75.75 0 0 1 1.06 0l3 3a.75.75 0 1 1-1.06 1.06l-1.72-1.72Z"}),S.createElement("path",{d:"M13.75 6.25a.75.75 0 0 0-1.5 0v7.69l-1.72-1.72a.75.75 0 1 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06l-1.72 1.72v-7.69Z"}))};Go.displayName="SortIcon";var Wo=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{d:"M13.03 6.97a.75.75 0 0 1 0 1.06l-1.97 1.97 1.97 1.97a.75.75 0 1 1-1.06 1.06l-1.97-1.97-1.97 1.97a.75.75 0 0 1-1.06-1.06l1.97-1.97-1.97-1.97a.75.75 0 0 1 1.06-1.06l1.97 1.97 1.97-1.97a.75.75 0 0 1 1.06 0Z"}),S.createElement("path",{fillRule:"evenodd",d:"M10 17a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm0-1.5a5.5 5.5 0 1 0 0-11 5.5 5.5 0 0 0 0 11Z"}))};Wo.displayName="XCircleIcon";var cr=function(t){return S.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),S.createElement("path",{d:"M13.97 15.03a.75.75 0 1 0 1.06-1.06l-3.97-3.97 3.97-3.97a.75.75 0 0 0-1.06-1.06l-3.97 3.97-3.97-3.97a.75.75 0 0 0-1.06 1.06l3.97 3.97-3.97 3.97a.75.75 0 1 0 1.06 1.06l3.97-3.97 3.97 3.97Z"}))};cr.displayName="XIcon";const ur="-",fi=e=>{const t=pi(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:a=>{const i=a.split(ur);return i[0]===""&&i.length!==1&&i.shift(),Uo(i,t)||gi(a)},getConflictingClassGroupIds:(a,i)=>{const u=n[a]||[];return i&&r[a]?[...u,...r[a]]:u}}},Uo=(e,t)=>{var a;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Uo(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(ur);return(a=t.validators.find(({validator:i})=>i(s)))==null?void 0:a.classGroupId},Wr=/^\[(.+)\]$/,gi=e=>{if(Wr.test(e)){const t=Wr.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},pi=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return hi(Object.entries(e.classGroups),n).forEach(([s,a])=>{Un(a,r,s,t)}),r},Un=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Ur(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(mi(o)){Un(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,a])=>{Un(a,Ur(t,s),n,r)})})},Ur=(e,t)=>{let n=e;return t.split(ur).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},mi=e=>e.isThemeGetter,hi=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([a,i])=>[t+a,i])):s);return[n,o]}):e,bi=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,a)=>{n.set(s,a),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let a=n.get(s);if(a!==void 0)return a;if((a=r.get(s))!==void 0)return o(s,a),a},set(s,a){n.has(s)?n.set(s,a):o(s,a)}}},qo="!",vi=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,a=i=>{const u=[];let l=0,d=0,c;for(let v=0;v<i.length;v++){let x=i[v];if(l===0){if(x===o&&(r||i.slice(v,v+s)===t)){u.push(i.slice(d,v)),d=v+s;continue}if(x==="/"){c=v;continue}}x==="["?l++:x==="]"&&l--}const g=u.length===0?i:i.substring(d),p=g.startsWith(qo),b=p?g.substring(1):g,h=c&&c>d?c-d:void 0;return{modifiers:u,hasImportantModifier:p,baseClassName:b,maybePostfixModifierPosition:h}};return n?i=>n({className:i,parseClassName:a}):a},xi=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},yi=e=>({cache:bi(e.cacheSize),parseClassName:vi(e),...fi(e)}),wi=/\s+/,Ci=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],a=e.trim().split(wi);let i="";for(let u=a.length-1;u>=0;u-=1){const l=a[u],{modifiers:d,hasImportantModifier:c,baseClassName:g,maybePostfixModifierPosition:p}=n(l);let b=!!p,h=r(b?g.substring(0,p):g);if(!h){if(!b){i=l+(i.length>0?" "+i:i);continue}if(h=r(g),!h){i=l+(i.length>0?" "+i:i);continue}b=!1}const v=xi(d).join(":"),x=c?v+qo:v,y=x+h;if(s.includes(y))continue;s.push(y);const w=o(h,b);for(let C=0;C<w.length;++C){const _=w[C];s.push(x+_)}i=l+(i.length>0?" "+i:i)}return i};function Si(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Xo(t))&&(r&&(r+=" "),r+=n);return r}const Xo=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Xo(e[r]))&&(n&&(n+=" "),n+=t);return n};function _i(e,...t){let n,r,o,s=a;function a(u){const l=t.reduce((d,c)=>c(d),e());return n=yi(l),r=n.cache.get,o=n.cache.set,s=i,i(u)}function i(u){const l=r(u);if(l)return l;const d=Ci(u,n);return o(u,d),d}return function(){return s(Si.apply(null,arguments))}}const ne=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Yo=/^\[(?:([a-z-]+):)?(.+)\]$/i,Ri=/^\d+\/\d+$/,Ei=new Set(["px","full","screen"]),Pi=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Mi=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Ni=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Ai=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,ji=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Be=e=>yt(e)||Ei.has(e)||Ri.test(e),Ye=e=>Et(e,"length",Vi),yt=e=>!!e&&!Number.isNaN(Number(e)),Nn=e=>Et(e,"number",yt),It=e=>!!e&&Number.isInteger(Number(e)),ki=e=>e.endsWith("%")&&yt(e.slice(0,-1)),W=e=>Yo.test(e),Ze=e=>Pi.test(e),Ii=new Set(["length","size","percentage"]),Fi=e=>Et(e,Ii,Zo),$i=e=>Et(e,"position",Zo),Oi=new Set(["image","url"]),Di=e=>Et(e,Oi,Bi),Ti=e=>Et(e,"",Li),Ft=()=>!0,Et=(e,t,n)=>{const r=Yo.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Vi=e=>Mi.test(e)&&!Ni.test(e),Zo=()=>!1,Li=e=>Ai.test(e),Bi=e=>ji.test(e),zi=()=>{const e=ne("colors"),t=ne("spacing"),n=ne("blur"),r=ne("brightness"),o=ne("borderColor"),s=ne("borderRadius"),a=ne("borderSpacing"),i=ne("borderWidth"),u=ne("contrast"),l=ne("grayscale"),d=ne("hueRotate"),c=ne("invert"),g=ne("gap"),p=ne("gradientColorStops"),b=ne("gradientColorStopPositions"),h=ne("inset"),v=ne("margin"),x=ne("opacity"),y=ne("padding"),w=ne("saturate"),C=ne("scale"),_=ne("sepia"),M=ne("skew"),E=ne("space"),R=ne("translate"),O=()=>["auto","contain","none"],T=()=>["auto","hidden","clip","visible","scroll"],D=()=>["auto",W,t],A=()=>[W,t],G=()=>["",Be,Ye],z=()=>["auto",yt,W],q=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],N=()=>["solid","dashed","dotted","double","none"],B=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],j=()=>["start","end","center","between","around","evenly","stretch"],$=()=>["","0",W],Z=()=>["auto","avoid","all","avoid-page","page","left","right","column"],P=()=>[yt,W];return{cacheSize:500,separator:":",theme:{colors:[Ft],spacing:[Be,Ye],blur:["none","",Ze,W],brightness:P(),borderColor:[e],borderRadius:["none","","full",Ze,W],borderSpacing:A(),borderWidth:G(),contrast:P(),grayscale:$(),hueRotate:P(),invert:$(),gap:A(),gradientColorStops:[e],gradientColorStopPositions:[ki,Ye],inset:D(),margin:D(),opacity:P(),padding:A(),saturate:P(),scale:P(),sepia:$(),skew:P(),space:A(),translate:A()},classGroups:{aspect:[{aspect:["auto","square","video",W]}],container:["container"],columns:[{columns:[Ze]}],"break-after":[{"break-after":Z()}],"break-before":[{"break-before":Z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...q(),W]}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",It,W]}],basis:[{basis:D()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",W]}],grow:[{grow:$()}],shrink:[{shrink:$()}],order:[{order:["first","last","none",It,W]}],"grid-cols":[{"grid-cols":[Ft]}],"col-start-end":[{col:["auto",{span:["full",It,W]},W]}],"col-start":[{"col-start":z()}],"col-end":[{"col-end":z()}],"grid-rows":[{"grid-rows":[Ft]}],"row-start-end":[{row:["auto",{span:[It,W]},W]}],"row-start":[{"row-start":z()}],"row-end":[{"row-end":z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",W]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",W]}],gap:[{gap:[g]}],"gap-x":[{"gap-x":[g]}],"gap-y":[{"gap-y":[g]}],"justify-content":[{justify:["normal",...j()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...j(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...j(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[y]}],px:[{px:[y]}],py:[{py:[y]}],ps:[{ps:[y]}],pe:[{pe:[y]}],pt:[{pt:[y]}],pr:[{pr:[y]}],pb:[{pb:[y]}],pl:[{pl:[y]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",W,t]}],"min-w":[{"min-w":[W,t,"min","max","fit"]}],"max-w":[{"max-w":[W,t,"none","full","min","max","fit","prose",{screen:[Ze]},Ze]}],h:[{h:[W,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[W,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ze,Ye]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Nn]}],"font-family":[{font:[Ft]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",W]}],"line-clamp":[{"line-clamp":["none",yt,Nn]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Be,W]}],"list-image":[{"list-image":["none",W]}],"list-style-type":[{list:["none","disc","decimal",W]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[x]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[x]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...N(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Be,Ye]}],"underline-offset":[{"underline-offset":["auto",Be,W]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[x]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...q(),$i]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Fi]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Di]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[x]}],"border-style":[{border:[...N(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[x]}],"divide-style":[{divide:N()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...N()]}],"outline-offset":[{"outline-offset":[Be,W]}],"outline-w":[{outline:[Be,Ye]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:G()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[x]}],"ring-offset-w":[{"ring-offset":[Be,Ye]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ze,Ti]}],"shadow-color":[{shadow:[Ft]}],opacity:[{opacity:[x]}],"mix-blend":[{"mix-blend":[...B(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":B()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",Ze,W]}],grayscale:[{grayscale:[l]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[w]}],sepia:[{sepia:[_]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[l]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[x]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",W]}],duration:[{duration:P()}],ease:[{ease:["linear","in","out","in-out",W]}],delay:[{delay:P()}],animate:[{animate:["none","spin","ping","pulse","bounce",W]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[C]}],"scale-x":[{"scale-x":[C]}],"scale-y":[{"scale-y":[C]}],rotate:[{rotate:[It,W]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[M]}],"skew-y":[{"skew-y":[M]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",W]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Be,Ye,Nn]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Hi=_i(zi);function F(...e){return Hi(Do(e))}const Ko=Q("cursor-pointer inline-flex items-center justify-center whitespace-nowrap transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-hidden focus-visible:ring-3 focus-visible:ring-offset-2 relative m-0 border-0 font-sans !text-xs font-medium !leading-[20.15px] !rounded-lg",{variants:{variant:{default:"bg-white text-gray-700 hover:bg-[#f6f6f6] shadow-[inset_0px_0.5px_0px_#c6c6c6,_0px_1px_1px_#c6c6c6,_inset_0.5px_0px_0px_#c6c6c6,_inset_-0.5px_0px_0px_#c6c6c6] hover:shadow-[inset_0px_0.5px_0px_#c6c6c6,_0px_1px_0px_#c6c6c6,_inset_0.5px_0px_0px_#c6c6c6,_inset_-0.5px_0px_0px_#c6c6c6] focus-visible:ring-gray-500/20",primary:"bg-gray-900 text-white hover:bg-gray-800 focus-visible:ring-gray-900/20",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus-visible:ring-gray-500/20",tertiary:"!border !border-gray-300 bg-white text-gray-900 hover:bg-gray-50 focus-visible:ring-gray-500/20",plain:"text-blue-600 hover:text-blue-700 hover:underline focus-visible:ring-blue-600/20 bg-transparent !shadow-none",monochromePlain:"text-gray-600 hover:text-gray-700 hover:underline focus-visible:ring-gray-500/20 bg-transparent !shadow-none"},size:{micro:"!h-5 !px-2 !py-0.5 gap-1 text-xs",slim:"!h-6 !px-2.5 !py-1 gap-1.5",medium:"!h-6 !px-4 !py-2 gap-2",large:"!h-7 !px-5 !py-2.5 gap-2.5 text-sm"},tone:{default:"",success:"bg-green-700 text-white hover:bg-green-800 focus-visible:ring-green-700/20",critical:"bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-600/20"},textAlign:{start:"justify-start text-left",center:"justify-center text-center",end:"justify-end text-right",left:"justify-start text-left",right:"justify-end text-right"},fullWidth:{true:"w-full",false:""},pressed:{true:"bg-accent text-accent-foreground",false:""}},defaultVariants:{variant:"default",size:"medium",tone:"default",textAlign:"center",fullWidth:!1,pressed:!1}}),Gi=e=>e?e===!0||e==="down"?f.jsx(sr,{className:"size-4"}):e==="up"?f.jsx(lr,{className:"size-4"}):e==="select"?f.jsx(Vo,{className:"size-4"}):null:null,qr=()=>f.jsx(Bo,{className:"size-4 animate-spin"}),oe=m.forwardRef(({children:e,size:t="medium",textAlign:n="center",fullWidth:r=!1,disclosure:o,removeUnderline:s,icon:a,dataPrimaryLink:i,tone:u="default",variant:l="default",id:d,url:c,external:g,target:p,download:b,submit:h,disabled:v,loading:x,pressed:y=!1,accessibilityLabel:w,role:C,ariaControls:_,ariaExpanded:M,ariaDescribedBy:E,ariaChecked:R,onClick:O,onFocus:T,onBlur:D,onKeyPress:A,onKeyUp:G,onKeyDown:z,onMouseEnter:q,onTouchStart:N,onPointerDown:B,className:j,asChild:$=!1,...Z},P)=>{const k=!!c,I=$?ai:k?"a":"button",X=x,te=v||X,U=Gi(o),J=F(Ko({variant:l,size:t,tone:u==="critical"?"critical":u==="success"?"success":"default",textAlign:n,fullWidth:r,pressed:y}),s&&(l==="plain"||l==="monochromePlain")&&"no-underline hover:no-underline",j),re={id:d,className:J,disabled:te,"aria-label":w,"aria-controls":_,"aria-expanded":M,"aria-describedby":E,"aria-checked":R,"aria-pressed":y,"data-primary-link":i,role:C,onClick:te?void 0:O,onFocus:T,onBlur:D,onKeyPress:A,onKeyUp:G,onKeyDown:z,onMouseEnter:q,onTouchStart:N,onPointerDown:B,ref:P,...Z};return k?f.jsxs(I,{...re,href:c,target:g?"_blank":p,rel:g?"noopener noreferrer":void 0,download:b,children:[a&&!X&&f.jsx("span",{className:"inline-flex shrink-0",children:a}),X&&f.jsx(qr,{}),e&&f.jsx("span",{className:"inline-flex",children:e}),U&&!X&&f.jsx("span",{className:"inline-flex shrink-0",children:U})]}):f.jsxs(I,{...re,type:h?"submit":"button",children:[a&&!X&&f.jsx("span",{className:"inline-flex shrink-0",children:a}),X&&f.jsx(qr,{}),e&&f.jsx("span",{className:"inline-flex",children:e}),U&&!X&&f.jsx("span",{className:"inline-flex shrink-0",children:U})]})});oe.displayName="Button";const Qo=Q("flex items-center",{variants:{gap:{extraTight:"gap-0.5",tight:"gap-2",loose:"gap-4"},variant:{segmented:"gap-0 [&>button]:ml-[-10px] [&>button]:[background-color:#ffffff] [&>button:hover]:[background-color:#f6f6f6] [&>button]:!rounded-none [&>button:first-child]:!rounded-tl-md [&>button:first-child]:!rounded-bl-md [&>button:last-child]:!rounded-tr-md [&>button:last-child]:!rounded-br-md"},fullWidth:{true:"w-full [&>*]:flex-1",false:""},connectedTop:{true:"[&>*]:rounded-t-none",false:""},noWrap:{true:"flex-nowrap",false:"flex-wrap"}},defaultVariants:{gap:"tight",fullWidth:!1,connectedTop:!1,noWrap:!1}}),dr=m.forwardRef(({children:e,gap:t="tight",variant:n,fullWidth:r=!1,connectedTop:o=!1,noWrap:s=!1,className:a,...i},u)=>{const l=F(Qo({gap:n==="segmented"?void 0:t,variant:n,fullWidth:r,connectedTop:o,noWrap:s}),a);return f.jsx("div",{className:l,ref:u,...i,children:e})});dr.displayName="ButtonGroup";const Wi=e=>e&&{bg:"bg-[rgb(241,241,241)]","bg-inverse":"bg-[rgb(26,26,26)]","bg-surface":"bg-white","bg-surface-hover":"bg-[rgb(247,247,247)]","bg-surface-active":"bg-[rgb(243,243,243)]","bg-surface-selected":"bg-[rgb(241,241,241)]","bg-surface-disabled":"bg-black/5","bg-surface-secondary":"bg-[rgb(247,247,247)]","bg-surface-secondary-hover":"bg-[rgb(241,241,241)]","bg-surface-secondary-active":"bg-[rgb(235,235,235)]","bg-surface-secondary-selected":"bg-[rgb(235,235,235)]","bg-surface-tertiary":"bg-[rgb(243,243,243)]","bg-surface-tertiary-hover":"bg-[rgb(235,235,235)]","bg-surface-tertiary-active":"bg-[rgb(227,227,227)]","bg-surface-brand":"bg-[rgb(227,227,227)]","bg-surface-brand-hover":"bg-[rgb(235,235,235)]","bg-surface-brand-active":"bg-[rgb(241,241,241)]","bg-surface-brand-selected":"bg-[rgb(241,241,241)]","bg-surface-info":"bg-[rgb(234,244,255)]","bg-surface-info-hover":"bg-[rgb(224,240,255)]","bg-surface-info-active":"bg-[rgb(202,230,255)]","bg-surface-success":"bg-[rgb(205,254,212)]","bg-surface-success-hover":"bg-[rgb(175,254,191)]","bg-surface-success-active":"bg-[rgb(146,252,172)]","bg-surface-caution":"bg-[rgb(255,248,219)]","bg-surface-caution-hover":"bg-[rgb(255,244,191)]","bg-surface-caution-active":"bg-[rgb(255,239,157)]","bg-surface-warning":"bg-[rgb(255,241,227)]","bg-surface-warning-hover":"bg-[rgb(255,235,213)]","bg-surface-warning-active":"bg-[rgb(255,228,198)]","bg-surface-critical":"bg-[rgb(254,232,235)]","bg-surface-critical-hover":"bg-[rgb(254,225,230)]","bg-surface-critical-active":"bg-[rgb(254,217,223)]","bg-surface-emphasis":"bg-[rgb(240,242,255)]","bg-surface-emphasis-hover":"bg-[rgb(234,237,255)]","bg-surface-emphasis-active":"bg-[rgb(226,231,255)]","bg-surface-magic":"bg-[rgb(248,247,255)]","bg-surface-magic-hover":"bg-[rgb(243,241,255)]","bg-surface-magic-active":"bg-[rgb(233,229,255)]","bg-surface-inverse":"bg-[rgb(48,48,48)]","bg-surface-transparent":"bg-transparent","bg-fill":"bg-white","bg-fill-hover":"bg-[rgb(250,250,250)]","bg-fill-active":"bg-[rgb(247,247,247)]","bg-fill-selected":"bg-[rgb(204,204,204)]","bg-fill-disabled":"bg-black/5","bg-fill-secondary":"bg-[rgb(241,241,241)]","bg-fill-secondary-hover":"bg-[rgb(235,235,235)]","bg-fill-secondary-active":"bg-[rgb(227,227,227)]","bg-fill-secondary-selected":"bg-[rgb(227,227,227)]","bg-fill-tertiary":"bg-[rgb(227,227,227)]","bg-fill-tertiary-hover":"bg-[rgb(212,212,212)]","bg-fill-tertiary-active":"bg-[rgb(204,204,204)]","bg-fill-brand":"bg-[rgb(48,48,48)]","bg-fill-brand-hover":"bg-[rgb(26,26,26)]","bg-fill-brand-active":"bg-[rgb(26,26,26)]","bg-fill-brand-selected":"bg-[rgb(48,48,48)]","bg-fill-brand-disabled":"bg-black/[0.17]","bg-fill-info":"bg-[rgb(145,208,255)]","bg-fill-info-hover":"bg-[rgb(81,192,255)]","bg-fill-info-active":"bg-[rgb(0,148,213)]","bg-fill-info-secondary":"bg-[rgb(213,235,255)]","bg-fill-success":"bg-[rgb(4,123,93)]","bg-fill-success-hover":"bg-[rgb(3,94,76)]","bg-fill-success-active":"bg-[rgb(1,75,64)]","bg-fill-success-secondary":"bg-[rgb(175,254,191)]","bg-fill-warning":"bg-[rgb(255,184,0)]","bg-fill-warning-hover":"bg-[rgb(229,165,0)]","bg-fill-warning-active":"bg-[rgb(178,132,0)]","bg-fill-warning-secondary":"bg-[rgb(255,214,164)]","bg-fill-caution":"bg-[rgb(255,230,0)]","bg-fill-caution-hover":"bg-[rgb(234,211,0)]","bg-fill-caution-active":"bg-[rgb(225,203,0)]","bg-fill-caution-secondary":"bg-[rgb(255,235,120)]","bg-fill-critical":"bg-[rgb(199,10,36)]","bg-fill-critical-hover":"bg-[rgb(163,10,36)]","bg-fill-critical-active":"bg-[rgb(142,11,33)]","bg-fill-critical-selected":"bg-[rgb(142,11,33)]","bg-fill-critical-secondary":"bg-[rgb(254,209,215)]","bg-fill-emphasis":"bg-[rgb(0,91,211)]","bg-fill-emphasis-hover":"bg-[rgb(0,66,153)]","bg-fill-emphasis-active":"bg-[rgb(0,46,106)]","bg-fill-magic":"bg-[rgb(128,81,255)]","bg-fill-magic-secondary":"bg-[rgb(233,229,255)]","bg-fill-magic-secondary-hover":"bg-[rgb(228,222,255)]","bg-fill-magic-secondary-active":"bg-[rgb(223,217,255)]","bg-fill-inverse":"bg-[rgb(48,48,48)]","bg-fill-inverse-hover":"bg-[rgb(74,74,74)]","bg-fill-inverse-active":"bg-[rgb(97,97,97)]","bg-fill-transparent":"bg-black/[0.02]","bg-fill-transparent-hover":"bg-black/5","bg-fill-transparent-active":"bg-black/[0.08]","bg-fill-transparent-selected":"bg-black/[0.08]","bg-fill-transparent-secondary":"bg-black/[0.06]","bg-fill-transparent-secondary-hover":"bg-black/[0.08]","bg-fill-transparent-secondary-active":"bg-black/[0.11]"}[e]||"",Xr=e=>e?e==="transparent"?"border-transparent":{border:"border-[rgb(227,227,227)]","border-hover":"border-[rgb(204,204,204)]","border-disabled":"border-[rgb(235,235,235)]","border-secondary":"border-[rgb(235,235,235)]","border-tertiary":"border-[rgb(204,204,204)]","border-focus":"border-[rgb(0,91,211)]","border-brand":"border-[rgb(227,227,227)]","border-info":"border-[rgb(168,216,255)]","border-success":"border-[rgb(146,252,172)]","border-caution":"border-[rgb(255,235,120)]","border-warning":"border-[rgb(255,200,121)]","border-critical":"border-[rgb(254,193,199)]","border-critical-secondary":"border-[rgb(142,11,33)]","border-emphasis":"border-[rgb(0,91,211)]","border-emphasis-hover":"border-[rgb(0,66,153)]","border-emphasis-active":"border-[rgb(0,46,106)]","border-magic":"border-[rgb(228,222,255)]","border-magic-secondary":"border-[rgb(148,116,255)]","border-magic-secondary-hover":"border-[rgb(128,81,255)]","border-inverse":"border-[rgb(97,97,97)]","border-inverse-hover":"border-[rgb(204,204,204)]","border-inverse-active":"border-[rgb(227,227,227)]"}[e]||"":"",Ui=e=>e&&{text:"text-[rgb(48,48,48)]","text-secondary":"text-[rgb(97,97,97)]","text-disabled":"text-[rgb(181,181,181)]","text-link":"text-[rgb(0,91,211)]","text-link-hover":"text-[rgb(0,66,153)]","text-link-active":"text-[rgb(0,46,106)]","text-brand":"text-[rgb(74,74,74)]","text-brand-hover":"text-[rgb(48,48,48)]","text-brand-on-bg-fill":"text-white","text-brand-on-bg-fill-hover":"text-[rgb(227,227,227)]","text-brand-on-bg-fill-active":"text-[rgb(204,204,204)]","text-brand-on-bg-fill-disabled":"text-white","text-info":"text-[rgb(0,58,90)]","text-info-hover":"text-[rgb(0,58,90)]","text-info-active":"text-[rgb(0,33,51)]","text-info-secondary":"text-[rgb(0,124,180)]","text-info-on-bg-fill":"text-[rgb(0,33,51)]","text-success":"text-[rgb(1,75,64)]","text-success-hover":"text-[rgb(7,54,48)]","text-success-active":"text-[rgb(2,38,34)]","text-success-secondary":"text-[rgb(4,123,93)]","text-success-on-bg-fill":"text-[rgb(250,255,251)]","text-caution":"text-[rgb(79,71,0)]","text-caution-hover":"text-[rgb(51,46,0)]","text-caution-active":"text-[rgb(31,28,0)]","text-caution-secondary":"text-[rgb(130,117,0)]","text-caution-on-bg-fill":"text-[rgb(51,46,0)]","text-warning":"text-[rgb(94,66,0)]","text-warning-hover":"text-[rgb(65,45,0)]","text-warning-active":"text-[rgb(37,26,0)]","text-warning-secondary":"text-[rgb(149,111,0)]","text-warning-on-bg-fill":"text-[rgb(37,26,0)]","text-critical":"text-[rgb(142,11,33)]","text-critical-hover":"text-[rgb(95,7,22)]","text-critical-active":"text-[rgb(47,4,11)]","text-critical-secondary":"text-[rgb(199,10,36)]","text-critical-on-bg-fill":"text-[rgb(255,250,251)]","text-emphasis":"text-[rgb(0,91,211)]","text-emphasis-hover":"text-[rgb(0,66,153)]","text-emphasis-active":"text-[rgb(0,46,106)]","text-emphasis-on-bg-fill":"text-[rgb(252,253,255)]","text-emphasis-on-bg-fill-hover":"text-[rgb(226,231,255)]","text-emphasis-on-bg-fill-active":"text-[rgb(213,220,255)]","text-magic":"text-[rgb(87,0,209)]","text-magic-secondary":"text-[rgb(113,38,255)]","text-magic-on-bg-fill":"text-[rgb(253,253,255)]","text-inverse":"text-[rgb(227,227,227)]","text-inverse-secondary":"text-[rgb(181,181,181)]","text-link-inverse":"text-[rgb(197,208,255)]"}[e]||"",Yr=e=>e&&{0:"0","025":"px","050":"0.5",100:"1",150:"1.5",200:"2",300:"3",400:"4",500:"5",600:"6",800:"8",1e3:"10",1200:"12",1600:"16",2e3:"20",2400:"24",2800:"28",3200:"32"}[e]||"",qi=e=>e&&{0:"rounded-none","050":"rounded-sm",100:"rounded",150:"rounded-md",200:"rounded-lg",300:"rounded-xl",400:"rounded-2xl",500:"rounded-3xl",750:"rounded-[30px]",full:"rounded-full"}[e]||"",Zr=e=>e&&{0:"border-0","0165":"border-[0.66px]","025":"border","050":"border-2",100:"border-4"}[e]||"",Xi=e=>e&&{0:"shadow-none",100:"shadow-[0px_1px_0px_0px_rgba(26,26,26,0.07)]",200:"shadow-[0px_3px_1px_-1px_rgba(26,26,26,0.07)]",300:"shadow-[0px_4px_6px_-2px_rgba(26,26,26,0.20)]",400:"shadow-[0px_8px_16px_-4px_rgba(26,26,26,0.22)]",500:"shadow-[0px_12px_20px_-8px_rgba(26,26,26,0.24)]",600:"shadow-[0px_20px_20px_-8px_rgba(26,26,26,0.28)]","bevel-100":"shadow-[1px_0px_0px_0px_rgba(0,0,0,0.13)_inset,_-1px_0px_0px_0px_rgba(0,0,0,0.13)_inset,_0px_-1px_0px_0px_rgba(0,0,0,0.17)_inset,_0px_1px_0px_0px_rgba(204,204,204,0.5)_inset]","inset-100":"shadow-[0px_1px_2px_0px_rgba(26,26,26,0.15)_inset,_0px_1px_1px_0px_rgba(26,26,26,0.15)_inset]","inset-200":"shadow-[0px_2px_1px_0px_rgba(26,26,26,0.20)_inset,_1px_0px_1px_0px_rgba(26,26,26,0.12)_inset,_-1px_0px_1px_0px_rgba(26,26,26,0.12)_inset]",button:"shadow-[0px_-1px_0px_0px_#b5b5b5_inset,_0px_0px_0px_1px_rgba(0,0,0,0.1)_inset,_0px_0.5px_0px_1.5px_#FFF_inset]","button-hover":"shadow-[0px_1px_0px_0px_#EBEBEB_inset,_-1px_0px_0px_0px_#EBEBEB_inset,_1px_0px_0px_0px_#EBEBEB_inset,_0px_-1px_0px_0px_#CCC_inset]","button-inset":"shadow-[-1px_0px_1px_0px_rgba(26,26,26,0.122)_inset,_1px_0px_1px_0px_rgba(26,26,26,0.122)_inset,_0px_2px_1px_0px_rgba(26,26,26,0.2)_inset]","button-primary":"shadow-[0px_-1px_0px_1px_rgba(0,0,0,0.8)_inset,_0px_0px_0px_1px_rgba(48,48,48,1)_inset,_0px_0.5px_0px_1.5px_rgba(255,255,255,0.25)_inset]","button-primary-hover":"shadow-[0px_1px_0px_0px_rgba(255,255,255,0.24)_inset,_1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_-1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_0px_-1px_0px_0px_#000_inset,_0px_-1px_0px_1px_#1A1A1A]","button-primary-inset":"shadow-[0px_3px_0px_0px_rgb(0,0,0)_inset]","button-primary-critical":"shadow-[0px_-1px_0px_1px_rgba(142,31,11,0.8)_inset,_0px_0px_0px_1px_rgba(181,38,11,0.8)_inset,_0px_0.5px_0px_1.5px_rgba(255,255,255,0.349)_inset]","button-primary-critical-hover":"shadow-[0px_1px_0px_0px_rgba(255,255,255,0.48)_inset,_1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_-1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_0px_-1.5px_0px_0px_rgba(0,0,0,0.25)_inset]","button-primary-critical-inset":"shadow-[-1px_0px_1px_0px_rgba(0,0,0,0.2)_inset,_1px_0px_1px_0px_rgba(0,0,0,0.2)_inset,_0px_2px_0px_0px_rgba(0,0,0,0.6)_inset]","button-primary-success":"shadow-[0px_-1px_0px_1px_rgba(12,81,50,0.8)_inset,_0px_0px_0px_1px_rgba(19,111,69,0.8)_inset,_0px_0.5px_0px_1.5px_rgba(255,255,255,0.251)_inset]","button-primary-success-hover":"shadow-[0px_1px_0px_0px_rgba(255,255,255,0.48)_inset,_1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_-1px_0px_0px_0px_rgba(255,255,255,0.20)_inset,_0px_-1.5px_0px_0px_rgba(0,0,0,0.25)_inset]","button-primary-success-inset":"shadow-[-1px_0px_1px_0px_rgba(0,0,0,0.2)_inset,_1px_0px_1px_0px_rgba(0,0,0,0.2)_inset,_0px_2px_0px_0px_rgba(0,0,0,0.6)_inset]","border-inset":"shadow-[0px_0px_0px_1px_rgba(0,0,0,0.08)_inset]"}[e]||"",Pe=(e,t="p")=>{if(!e)return"";if(typeof e=="string"){const r=Yr(e);return r?`${t}-${r}`:""}const n=[];return Object.entries(e).forEach(([r,o])=>{const s=Yr(o);if(s){const a=r==="xs"?"":`${r}:`;n.push(`${a}${t}-${s}`)}}),n.join(" ")},Yt=(e,t)=>{const r={0:"0","0165":"[0.66px]","025":"","050":"2",100:"4"}[e];return r?`border-${t}-${r}`:`border-${t}`},Zt=(e,t)=>{const r={0:"none","050":"sm",100:"",150:"md",200:"lg",300:"xl",400:"2xl",500:"3xl",750:"[30px]",full:"full"}[e];return r?`rounded-${t}-${r}`:`rounded-${t}`},Jo=Q("",{variants:{visuallyHidden:{true:"sr-only",false:""},printHidden:{true:"print:hidden",false:""}},defaultVariants:{visuallyHidden:!1,printHidden:!1}}),es=m.forwardRef(({children:e,as:t="div",background:n,borderColor:r,borderStyle:o,borderRadius:s,borderEndStartRadius:a,borderEndEndRadius:i,borderStartStartRadius:u,borderStartEndRadius:l,borderWidth:d,borderBlockStartWidth:c,borderBlockEndWidth:g,borderInlineStartWidth:p,borderInlineEndWidth:b,color:h,id:v,minHeight:x,minWidth:y,maxWidth:w,overflowX:C,overflowY:_,padding:M,paddingBlock:E,paddingBlockStart:R,paddingBlockEnd:O,paddingInline:T,paddingInlineStart:D,paddingInlineEnd:A,role:G,shadow:z,tabIndex:q,width:N,position:B,insetBlockStart:j,insetBlockEnd:$,insetInlineStart:Z,insetInlineEnd:P,opacity:k,outlineColor:I,outlineStyle:X,outlineWidth:te,printHidden:U=!1,visuallyHidden:J=!1,zIndex:re,className:ue,...pe},_e)=>{const K=t,le=F(Jo({visuallyHidden:J,printHidden:U}),Wi(n),Xr(r),o==="dashed"?"border-dashed":"border-solid",qi(s),Zr(d),c&&Yt(c,"t"),g&&Yt(g,"b"),p&&Yt(p,"l"),b&&Yt(b,"r"),a&&Zt(a,"bl"),i&&Zt(i,"br"),u&&Zt(u,"tl"),l&&Zt(l,"tr"),Ui(h),C&&`overflow-x-${C}`,_&&`overflow-y-${_}`,Pe(M,"p"),Pe(E,"py"),Pe(R,"pt"),Pe(O,"pb"),Pe(T,"px"),Pe(D,"pl"),Pe(A,"pr"),Xi(z),B&&`${B}`,Pe(j,"top"),Pe($,"bottom"),Pe(Z,"left"),Pe(P,"right"),I&&Xr(I).replace("border-","outline-"),X==="dashed"?"outline-dashed":X==="solid"?"outline-solid":"",te&&Zr(te).replace("border-","outline-"),ue),ve={...x&&{minHeight:x},...y&&{minWidth:y},...w&&{maxWidth:w},...N&&{width:N},...k&&{opacity:k},...re&&{zIndex:re}};return f.jsx(K,{ref:_e,className:le,style:Object.keys(ve).length>0?ve:void 0,id:v,role:G,tabIndex:q,...pe,children:e})});es.displayName="Box";const ts=Q("",{variants:{variant:{heading3xl:"text-4xl leading-tight sm:text-5xl sm:leading-tight",heading2xl:"text-3xl leading-tight sm:text-4xl sm:leading-tight",headingXl:"text-2xl leading-tight sm:text-3xl sm:leading-tight",headingLg:"text-xl leading-normal sm:text-2xl sm:leading-normal",headingMd:"text-sm leading-normal",headingSm:"text-xs leading-normal",headingXs:"text-xs leading-tight",bodyLg:"text-sm leading-normal",bodyMd:"text-xs leading-normal",bodySm:"text-xs leading-tight",bodyXs:"text-xs leading-tight"},alignment:{start:"text-left",center:"text-center",end:"text-right",justify:"text-justify"},tone:{base:"text-gray-900",disabled:"text-gray-400",inherit:"text-inherit",success:"text-green-600",critical:"text-red-600",caution:"text-yellow-600",subdued:"text-gray-600","text-inverse":"text-white","text-inverse-secondary":"text-gray-200",magic:"text-purple-600","magic-subdued":"text-purple-400"},fontWeight:{regular:"font-normal",medium:"font-medium",semibold:"font-semibold",bold:"font-bold"},breakWord:{true:"break-words",false:""},truncate:{true:"truncate",false:""},numeric:{true:"font-mono tabular-nums",false:""},visuallyHidden:{true:"sr-only",false:""},textDecorationLine:{"line-through":"line-through",none:""}},defaultVariants:{variant:"bodyMd",alignment:"start",tone:"base",fontWeight:"regular",breakWord:!1,truncate:!1,numeric:!1,visuallyHidden:!1,textDecorationLine:"none"}}),ye=m.forwardRef(({children:e,as:t="span",alignment:n,breakWord:r=!1,tone:o,fontWeight:s,id:a,numeric:i=!1,truncate:u=!1,variant:l="bodyMd",visuallyHidden:d=!1,textDecorationLine:c,className:g,...p},b)=>{const h=t,v=()=>{if(s)return s;switch(l){case"heading3xl":case"heading2xl":case"headingXl":return"bold";case"headingLg":case"headingMd":case"headingSm":case"headingXs":return"semibold";default:return"regular"}};return f.jsx(h,{ref:b,id:a,className:F(ts({variant:l,alignment:n,tone:o,fontWeight:v(),breakWord:r,truncate:u,numeric:i,visuallyHidden:d,textDecorationLine:c?"line-through":"none"}),g),...p,children:e})});ye.displayName="Text";const ns=Q(["relative flex items-center w-full","font-sans text-[13px] leading-[20px] font-normal","text-[rgb(48,48,48)] placeholder:text-[rgb(97,97,97)]","bg-white border border-[rgb(138,143,148)] border-t-[rgb(137,143,148)]","rounded-lg","px-3 py-[6px]","h-8 min-h-8","flex-grow flex-shrink","appearance-none outline-hidden cursor-text","focus-visible:outline-hidden focus-visible:border-[rgb(0,123,255)] focus-visible:border-2","focus-visible:bg-white focus-visible:ring-3 focus-visible:ring-[rgb(0,123,255)] focus-visible:ring-offset-1","hover:border-[rgb(97,97,97)] hover:bg-[rgb(247,247,247)]","disabled:cursor-default disabled:text-[rgb(181,181,181)] disabled:bg-[rgb(247,247,247)] disabled:border-transparent","file:border-0 file:bg-transparent file:text-[13px] file:font-normal"],{variants:{size:{slim:"h-7 min-h-7 py-[2px] text-[13px] leading-[20px]",medium:"h-8 min-h-8 py-[6px] text-[13px] leading-[20px]"},variant:{inherit:"",borderless:["border-0 shadow-none min-h-8","focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-0","hover:border-0 hover:bg-transparent"]},align:{left:"text-left",center:"text-center",right:"text-right"},tone:{magic:["text-[rgb(128,81,255)] bg-[rgb(248,247,255)]","border-[rgb(153,124,255)]","focus-visible:border-[rgb(0,123,255)] focus-visible:text-[rgb(48,48,48)]","hover:bg-[rgb(243,240,255)] hover:border-[rgb(138,129,255)]"]},error:{true:["border-[rgb(253,75,146)] bg-[rgb(255,247,250)]","focus-visible:border-[rgb(0,123,255)] focus-visible:bg-white","hover:border-[rgb(253,75,146)] hover:bg-[rgb(255,247,250)]"],false:""},monospaced:{true:"font-mono",false:"font-sans"},multiline:{true:["min-h-[80px] resize-y overflow-auto","items-start py-[6px]","leading-[20px]"],false:"items-center"}},defaultVariants:{size:"medium",variant:"inherit",align:"left",error:!1,monospaced:!1,multiline:!1}}),Yi=()=>{const[e]=m.useState(()=>Math.random().toString(36).substring(2,11));return e},Zi=e=>{if(e)return typeof e=="number"?e:3},Ki=e=>{if(e)return e||typeof e=="number"&&e>0?{"aria-multiline":!0}:void 0},Kr=e=>`${e}Label`,Qr=e=>`${e}HelpText`,fr=m.forwardRef(({prefix:e,suffix:t,verticalContent:n,placeholder:r,value:o="",helpText:s,label:a,labelAction:i,labelHidden:u=!1,disabled:l=!1,clearButton:d=!1,readOnly:c=!1,autoFocus:g=!1,focused:p,multiline:b=!1,error:h,connectedRight:v,connectedLeft:x,type:y="text",name:w,id:C,role:_,step:M,largeStep:E,autoComplete:R,max:O,maxLength:T,maxHeight:D,min:A,minLength:G,pattern:z,inputMode:q,spellCheck:N,ariaOwns:B,ariaControls:j,ariaExpanded:$,ariaActiveDescendant:Z,ariaAutocomplete:P,showCharacterCount:k=!1,align:I="left",requiredIndicator:X=!1,monospaced:te=!1,selectTextOnFocus:U=!1,suggestion:J,variant:re="inherit",size:ue="medium",tone:pe,autoSize:_e=!1,loading:K=!1,onClearButtonClick:le,onChange:ve,onSpinnerChange:zt,onFocus:We,onBlur:Re,className:pt,...de},st)=>{const En=Yi(),Y=C??En,[Ue,Ht]=m.useState(!!p),At=m.useRef(null),at=m.useRef(null),Gt=m.useRef(null),it=m.useCallback(()=>b?at.current:At.current,[b]);m.useEffect(()=>{const ee=it();!ee||p===void 0||(p?ee.focus():ee.blur())},[p,it]),m.useEffect(()=>{const ee=At.current;!ee||!(y==="text"||y==="tel"||y==="search"||y==="url"||y==="password")||!J||ee.setSelectionRange(o.length,J.length)},[Ue,o,y,J]);const lt=J||o,qe=y==="currency"?"text":y,Wt=F(ns({size:ue,variant:re,align:I,tone:pe,error:!!h,monospaced:te,multiline:!!b}),_e&&"w-auto min-w-0",pt),Ve=m.useCallback(ee=>{ve&&ve(ee.currentTarget.value,Y)},[ve,Y]),Pn=m.useCallback(ee=>{if(Ht(!0),U&&!J){const se=it();se==null||se.select()}We&&We(ee)},[U,J,it,We]),Mn=m.useCallback(ee=>{var se;Ht(!1),!((se=Gt.current)!=null&&se.contains(ee==null?void 0:ee.relatedTarget))&&Re&&Re(ee)},[Re]),Ut=m.useCallback(()=>{le&&le(Y)},[le,Y]),Xe=[];h&&Xe.push(`${Y}Error`),s&&Xe.push(Qr(Y)),k&&Xe.push(`${Y}-CharacterCounter`);const ct=[];e&&ct.push(`${Y}-Prefix`),t&&ct.push(`${Y}-Suffix`),n&&ct.push(`${Y}-VerticalContent`),ct.unshift(Kr(Y));let mt=null;if(k){const ee=lt.length,se=T?`${ee}/${T}`:ee;mt=f.jsx("div",{id:`${Y}-CharacterCounter`,className:"text-xs text-muted-foreground mt-1",children:se})}const qt=d&&lt!==""?f.jsx("button",{type:"button",className:"absolute right-2 top-1/2 -translate-y-1/2 p-1 text-muted-foreground hover:text-foreground focus:outline-hidden focus:ring-3 focus:ring-ring focus:ring-offset-2 rounded-sm",onClick:Ut,tabIndex:-1,"aria-label":"Clear",children:f.jsx(Wo,{className:"h-4 w-4"})}):null,Xt=e?f.jsx("div",{id:`${Y}-Prefix`,className:"flex items-center px-3 text-sm text-muted-foreground border-r border-input bg-muted/50",children:e}):null,jt=t?f.jsx("div",{id:`${Y}-Suffix`,className:"flex items-center px-3 text-sm text-muted-foreground border-l border-input bg-muted/50",children:t}):null,kt=K?f.jsx("div",{className:"absolute right-2 top-1/2 -translate-y-1/2",children:f.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-foreground"})}):null,H=u?null:f.jsxs("label",{id:Kr(Y),htmlFor:Y,className:F("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",X&&"after:content-['*'] after:ml-1 after:text-destructive"),children:[a,i&&f.jsx("button",{type:"button",onClick:i.onAction,className:"ml-2 text-xs text-primary hover:underline focus:outline-hidden focus:underline",children:i.content})]}),ae=s?f.jsx("div",{id:Qr(Y),className:"text-xs text-muted-foreground mt-1",children:s}):null,ce=h&&typeof h!="boolean"?f.jsx("div",{id:`${Y}Error`,className:"text-xs text-destructive mt-1",role:"alert",children:h}):null,fe=n?f.jsx("div",{id:`${Y}-VerticalContent`,className:"text-sm text-muted-foreground mb-2",children:n}):null,ut={...D&&{maxHeight:D}},ie=m.createElement(b?"textarea":"input",{ref:m.useCallback(ee=>{b?at.current=ee:At.current=ee,typeof st=="function"?st(ee):st&&(st.current=ee)},[b,st]),id:Y,name:w,type:qe,value:lt,placeholder:r,disabled:l,readOnly:c,autoFocus:g,role:_,min:A,max:O,step:M,minLength:G,maxLength:T,pattern:z,inputMode:q,spellCheck:N,autoComplete:R,rows:Zi(b),size:_e?1:void 0,style:Object.keys(ut).length>0?ut:void 0,className:Wt,"aria-describedby":Xe.length?Xe.join(" "):void 0,"aria-labelledby":ct.join(" "),"aria-invalid":!!h,"aria-owns":B,"aria-activedescendant":Z,"aria-autocomplete":P,"aria-controls":j,"aria-expanded":$,"aria-required":X,...Ki(b),onChange:J?void 0:Ve,onInput:J?Ve:void 0,onFocus:Pn,onBlur:Mn,"data-1p-ignore":R==="off"||void 0,"data-lpignore":R==="off"||void 0,"data-form-type":R==="off"?"other":void 0,...de}),me=x||v?f.jsxs("div",{className:"flex",children:[x,f.jsxs("div",{className:"flex-1 relative",children:[fe,f.jsxs("div",{className:"relative flex",children:[Xt,ie,jt,qt,kt]})]}),v]}):f.jsxs("div",{className:"relative",children:[fe,f.jsxs("div",{className:"relative flex",children:[Xt,ie,jt,qt,kt]})]});return f.jsxs("div",{ref:Gt,className:"space-y-2",children:[H,me,ce,ae,mt]})});fr.displayName="TextField";function we(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Qi(e,t){const n=m.createContext(t),r=s=>{const{children:a,...i}=s,u=m.useMemo(()=>i,Object.values(i));return f.jsx(n.Provider,{value:u,children:a})};r.displayName=e+"Provider";function o(s){const a=m.useContext(n);if(a)return a;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function gr(e,t=[]){let n=[];function r(s,a){const i=m.createContext(a),u=n.length;n=[...n,a];const l=c=>{var x;const{scope:g,children:p,...b}=c,h=((x=g==null?void 0:g[e])==null?void 0:x[u])||i,v=m.useMemo(()=>b,Object.values(b));return f.jsx(h.Provider,{value:v,children:p})};l.displayName=s+"Provider";function d(c,g){var h;const p=((h=g==null?void 0:g[e])==null?void 0:h[u])||i,b=m.useContext(p);if(b)return b;if(a!==void 0)return a;throw new Error(`\`${c}\` must be used within \`${s}\``)}return[l,d]}const o=()=>{const s=n.map(a=>m.createContext(a));return function(i){const u=(i==null?void 0:i[e])||s;return m.useMemo(()=>({[`__scope${e}`]:{...i,[e]:u}}),[i,u])}};return o.scopeName=e,[r,Ji(o,...t)]}function Ji(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const a=r.reduce((i,{useScope:u,scopeName:l})=>{const c=u(s)[`__scope${l}`];return{...i,...c}},{});return m.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}var el=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],be=el.reduce((e,t)=>{const n=bn(`Primitive.${t}`),r=m.forwardRef((o,s)=>{const{asChild:a,...i}=o,u=a?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(u,{...i,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function tl(e,t){e&&Fo.flushSync(()=>e.dispatchEvent(t))}function St(e){const t=m.useRef(e);return m.useEffect(()=>{t.current=e}),m.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function nl(e,t=globalThis==null?void 0:globalThis.document){const n=St(e);m.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var rl="DismissableLayer",qn="dismissableLayer.update",ol="dismissableLayer.pointerDownOutside",sl="dismissableLayer.focusOutside",Jr,rs=m.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),pr=m.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:a,onDismiss:i,...u}=e,l=m.useContext(rs),[d,c]=m.useState(null),g=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,p]=m.useState({}),b=je(t,E=>c(E)),h=Array.from(l.layers),[v]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),x=h.indexOf(v),y=d?h.indexOf(d):-1,w=l.layersWithOutsidePointerEventsDisabled.size>0,C=y>=x,_=ll(E=>{const R=E.target,O=[...l.branches].some(T=>T.contains(R));!C||O||(o==null||o(E),a==null||a(E),E.defaultPrevented||i==null||i())},g),M=cl(E=>{const R=E.target;[...l.branches].some(T=>T.contains(R))||(s==null||s(E),a==null||a(E),E.defaultPrevented||i==null||i())},g);return nl(E=>{y===l.layers.size-1&&(r==null||r(E),!E.defaultPrevented&&i&&(E.preventDefault(),i()))},g),m.useEffect(()=>{if(d)return n&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(Jr=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(d)),l.layers.add(d),eo(),()=>{n&&l.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=Jr)}},[d,g,n,l]),m.useEffect(()=>()=>{d&&(l.layers.delete(d),l.layersWithOutsidePointerEventsDisabled.delete(d),eo())},[d,l]),m.useEffect(()=>{const E=()=>p({});return document.addEventListener(qn,E),()=>document.removeEventListener(qn,E)},[]),f.jsx(be.div,{...u,ref:b,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:we(e.onFocusCapture,M.onFocusCapture),onBlurCapture:we(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:we(e.onPointerDownCapture,_.onPointerDownCapture)})});pr.displayName=rl;var al="DismissableLayerBranch",il=m.forwardRef((e,t)=>{const n=m.useContext(rs),r=m.useRef(null),o=je(t,r);return m.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),f.jsx(be.div,{...e,ref:o})});il.displayName=al;function ll(e,t=globalThis==null?void 0:globalThis.document){const n=St(e),r=m.useRef(!1),o=m.useRef(()=>{});return m.useEffect(()=>{const s=i=>{if(i.target&&!r.current){let u=function(){os(ol,n,l,{discrete:!0})};const l={originalEvent:i};i.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function cl(e,t=globalThis==null?void 0:globalThis.document){const n=St(e),r=m.useRef(!1);return m.useEffect(()=>{const o=s=>{s.target&&!r.current&&os(sl,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function eo(){const e=new CustomEvent(qn);document.dispatchEvent(e)}function os(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?tl(o,s):o.dispatchEvent(s)}var An=0;function ss(){m.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??to()),document.body.insertAdjacentElement("beforeend",e[1]??to()),An++,()=>{An===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),An--}},[])}function to(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var jn="focusScope.autoFocusOnMount",kn="focusScope.autoFocusOnUnmount",no={bubbles:!1,cancelable:!0},ul="FocusScope",mr=m.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...a}=e,[i,u]=m.useState(null),l=St(o),d=St(s),c=m.useRef(null),g=je(t,h=>u(h)),p=m.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;m.useEffect(()=>{if(r){let h=function(w){if(p.paused||!i)return;const C=w.target;i.contains(C)?c.current=C:Qe(c.current,{select:!0})},v=function(w){if(p.paused||!i)return;const C=w.relatedTarget;C!==null&&(i.contains(C)||Qe(c.current,{select:!0}))},x=function(w){if(document.activeElement===document.body)for(const _ of w)_.removedNodes.length>0&&Qe(i)};document.addEventListener("focusin",h),document.addEventListener("focusout",v);const y=new MutationObserver(x);return i&&y.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",h),document.removeEventListener("focusout",v),y.disconnect()}}},[r,i,p.paused]),m.useEffect(()=>{if(i){oo.add(p);const h=document.activeElement;if(!i.contains(h)){const x=new CustomEvent(jn,no);i.addEventListener(jn,l),i.dispatchEvent(x),x.defaultPrevented||(dl(hl(as(i)),{select:!0}),document.activeElement===h&&Qe(i))}return()=>{i.removeEventListener(jn,l),setTimeout(()=>{const x=new CustomEvent(kn,no);i.addEventListener(kn,d),i.dispatchEvent(x),x.defaultPrevented||Qe(h??document.body,{select:!0}),i.removeEventListener(kn,d),oo.remove(p)},0)}}},[i,l,d,p]);const b=m.useCallback(h=>{if(!n&&!r||p.paused)return;const v=h.key==="Tab"&&!h.altKey&&!h.ctrlKey&&!h.metaKey,x=document.activeElement;if(v&&x){const y=h.currentTarget,[w,C]=fl(y);w&&C?!h.shiftKey&&x===C?(h.preventDefault(),n&&Qe(w,{select:!0})):h.shiftKey&&x===w&&(h.preventDefault(),n&&Qe(C,{select:!0})):x===y&&h.preventDefault()}},[n,r,p.paused]);return f.jsx(be.div,{tabIndex:-1,...a,ref:g,onKeyDown:b})});mr.displayName=ul;function dl(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Qe(r,{select:t}),document.activeElement!==n)return}function fl(e){const t=as(e),n=ro(t,e),r=ro(t.reverse(),e);return[n,r]}function as(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function ro(e,t){for(const n of e)if(!gl(n,{upTo:t}))return n}function gl(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function pl(e){return e instanceof HTMLInputElement&&"select"in e}function Qe(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&pl(e)&&t&&e.select()}}var oo=ml();function ml(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=so(e,t),e.unshift(t)},remove(t){var n;e=so(e,t),(n=e[0])==null||n.resume()}}}function so(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function hl(e){return e.filter(t=>t.tagName!=="A")}var et=globalThis!=null&&globalThis.document?m.useLayoutEffect:()=>{},bl=m[" useId ".trim().toString()]||(()=>{}),vl=0;function ln(e){const[t,n]=m.useState(bl());return et(()=>{n(r=>r??String(vl++))},[e]),e||(t?`radix-${t}`:"")}const xl=["top","right","bottom","left"],tt=Math.min,xe=Math.max,fn=Math.round,Kt=Math.floor,Oe=e=>({x:e,y:e}),yl={left:"right",right:"left",bottom:"top",top:"bottom"},wl={start:"end",end:"start"};function Xn(e,t,n){return xe(e,tt(t,n))}function He(e,t){return typeof e=="function"?e(t):e}function Ge(e){return e.split("-")[0]}function Pt(e){return e.split("-")[1]}function hr(e){return e==="x"?"y":"x"}function br(e){return e==="y"?"height":"width"}const Cl=new Set(["top","bottom"]);function $e(e){return Cl.has(Ge(e))?"y":"x"}function vr(e){return hr($e(e))}function Sl(e,t,n){n===void 0&&(n=!1);const r=Pt(e),o=vr(e),s=br(o);let a=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(a=gn(a)),[a,gn(a)]}function _l(e){const t=gn(e);return[Yn(e),t,Yn(t)]}function Yn(e){return e.replace(/start|end/g,t=>wl[t])}const ao=["left","right"],io=["right","left"],Rl=["top","bottom"],El=["bottom","top"];function Pl(e,t,n){switch(e){case"top":case"bottom":return n?t?io:ao:t?ao:io;case"left":case"right":return t?Rl:El;default:return[]}}function Ml(e,t,n,r){const o=Pt(e);let s=Pl(Ge(e),n==="start",r);return o&&(s=s.map(a=>a+"-"+o),t&&(s=s.concat(s.map(Yn)))),s}function gn(e){return e.replace(/left|right|bottom|top/g,t=>yl[t])}function Nl(e){return{top:0,right:0,bottom:0,left:0,...e}}function is(e){return typeof e!="number"?Nl(e):{top:e,right:e,bottom:e,left:e}}function pn(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function lo(e,t,n){let{reference:r,floating:o}=e;const s=$e(t),a=vr(t),i=br(a),u=Ge(t),l=s==="y",d=r.x+r.width/2-o.width/2,c=r.y+r.height/2-o.height/2,g=r[i]/2-o[i]/2;let p;switch(u){case"top":p={x:d,y:r.y-o.height};break;case"bottom":p={x:d,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:c};break;case"left":p={x:r.x-o.width,y:c};break;default:p={x:r.x,y:r.y}}switch(Pt(t)){case"start":p[a]-=g*(n&&l?-1:1);break;case"end":p[a]+=g*(n&&l?-1:1);break}return p}const Al=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:a}=n,i=s.filter(Boolean),u=await(a.isRTL==null?void 0:a.isRTL(t));let l=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:c}=lo(l,r,u),g=r,p={},b=0;for(let h=0;h<i.length;h++){const{name:v,fn:x}=i[h],{x:y,y:w,data:C,reset:_}=await x({x:d,y:c,initialPlacement:r,placement:g,strategy:o,middlewareData:p,rects:l,platform:a,elements:{reference:e,floating:t}});d=y??d,c=w??c,p={...p,[v]:{...p[v],...C}},_&&b<=50&&(b++,typeof _=="object"&&(_.placement&&(g=_.placement),_.rects&&(l=_.rects===!0?await a.getElementRects({reference:e,floating:t,strategy:o}):_.rects),{x:d,y:c}=lo(l,g,u)),h=-1)}return{x:d,y:c,placement:g,strategy:o,middlewareData:p}};async function Tt(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:a,elements:i,strategy:u}=e,{boundary:l="clippingAncestors",rootBoundary:d="viewport",elementContext:c="floating",altBoundary:g=!1,padding:p=0}=He(t,e),b=is(p),v=i[g?c==="floating"?"reference":"floating":c],x=pn(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(v)))==null||n?v:v.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(i.floating)),boundary:l,rootBoundary:d,strategy:u})),y=c==="floating"?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await(s.getOffsetParent==null?void 0:s.getOffsetParent(i.floating)),C=await(s.isElement==null?void 0:s.isElement(w))?await(s.getScale==null?void 0:s.getScale(w))||{x:1,y:1}:{x:1,y:1},_=pn(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:y,offsetParent:w,strategy:u}):y);return{top:(x.top-_.top+b.top)/C.y,bottom:(_.bottom-x.bottom+b.bottom)/C.y,left:(x.left-_.left+b.left)/C.x,right:(_.right-x.right+b.right)/C.x}}const jl=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:a,elements:i,middlewareData:u}=t,{element:l,padding:d=0}=He(e,t)||{};if(l==null)return{};const c=is(d),g={x:n,y:r},p=vr(o),b=br(p),h=await a.getDimensions(l),v=p==="y",x=v?"top":"left",y=v?"bottom":"right",w=v?"clientHeight":"clientWidth",C=s.reference[b]+s.reference[p]-g[p]-s.floating[b],_=g[p]-s.reference[p],M=await(a.getOffsetParent==null?void 0:a.getOffsetParent(l));let E=M?M[w]:0;(!E||!await(a.isElement==null?void 0:a.isElement(M)))&&(E=i.floating[w]||s.floating[b]);const R=C/2-_/2,O=E/2-h[b]/2-1,T=tt(c[x],O),D=tt(c[y],O),A=T,G=E-h[b]-D,z=E/2-h[b]/2+R,q=Xn(A,z,G),N=!u.arrow&&Pt(o)!=null&&z!==q&&s.reference[b]/2-(z<A?T:D)-h[b]/2<0,B=N?z<A?z-A:z-G:0;return{[p]:g[p]+B,data:{[p]:q,centerOffset:z-q-B,...N&&{alignmentOffset:B}},reset:N}}}),kl=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:a,initialPlacement:i,platform:u,elements:l}=t,{mainAxis:d=!0,crossAxis:c=!0,fallbackPlacements:g,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:b="none",flipAlignment:h=!0,...v}=He(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const x=Ge(o),y=$e(i),w=Ge(i)===i,C=await(u.isRTL==null?void 0:u.isRTL(l.floating)),_=g||(w||!h?[gn(i)]:_l(i)),M=b!=="none";!g&&M&&_.push(...Ml(i,h,b,C));const E=[i,..._],R=await Tt(t,v),O=[];let T=((r=s.flip)==null?void 0:r.overflows)||[];if(d&&O.push(R[x]),c){const z=Sl(o,a,C);O.push(R[z[0]],R[z[1]])}if(T=[...T,{placement:o,overflows:O}],!O.every(z=>z<=0)){var D,A;const z=(((D=s.flip)==null?void 0:D.index)||0)+1,q=E[z];if(q&&(!(c==="alignment"?y!==$e(q):!1)||T.every(j=>j.overflows[0]>0&&$e(j.placement)===y)))return{data:{index:z,overflows:T},reset:{placement:q}};let N=(A=T.filter(B=>B.overflows[0]<=0).sort((B,j)=>B.overflows[1]-j.overflows[1])[0])==null?void 0:A.placement;if(!N)switch(p){case"bestFit":{var G;const B=(G=T.filter(j=>{if(M){const $=$e(j.placement);return $===y||$==="y"}return!0}).map(j=>[j.placement,j.overflows.filter($=>$>0).reduce(($,Z)=>$+Z,0)]).sort((j,$)=>j[1]-$[1])[0])==null?void 0:G[0];B&&(N=B);break}case"initialPlacement":N=i;break}if(o!==N)return{reset:{placement:N}}}return{}}}};function co(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function uo(e){return xl.some(t=>e[t]>=0)}const Il=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=He(e,t);switch(r){case"referenceHidden":{const s=await Tt(t,{...o,elementContext:"reference"}),a=co(s,n.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:uo(a)}}}case"escaped":{const s=await Tt(t,{...o,altBoundary:!0}),a=co(s,n.floating);return{data:{escapedOffsets:a,escaped:uo(a)}}}default:return{}}}}},ls=new Set(["left","top"]);async function Fl(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),a=Ge(n),i=Pt(n),u=$e(n)==="y",l=ls.has(a)?-1:1,d=s&&u?-1:1,c=He(t,e);let{mainAxis:g,crossAxis:p,alignmentAxis:b}=typeof c=="number"?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return i&&typeof b=="number"&&(p=i==="end"?b*-1:b),u?{x:p*d,y:g*l}:{x:g*l,y:p*d}}const $l=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:a,middlewareData:i}=t,u=await Fl(t,e);return a===((n=i.offset)==null?void 0:n.placement)&&(r=i.arrow)!=null&&r.alignmentOffset?{}:{x:o+u.x,y:s+u.y,data:{...u,placement:a}}}}},Ol=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:a=!1,limiter:i={fn:v=>{let{x,y}=v;return{x,y}}},...u}=He(e,t),l={x:n,y:r},d=await Tt(t,u),c=$e(Ge(o)),g=hr(c);let p=l[g],b=l[c];if(s){const v=g==="y"?"top":"left",x=g==="y"?"bottom":"right",y=p+d[v],w=p-d[x];p=Xn(y,p,w)}if(a){const v=c==="y"?"top":"left",x=c==="y"?"bottom":"right",y=b+d[v],w=b-d[x];b=Xn(y,b,w)}const h=i.fn({...t,[g]:p,[c]:b});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[g]:s,[c]:a}}}}}},Dl=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:a}=t,{offset:i=0,mainAxis:u=!0,crossAxis:l=!0}=He(e,t),d={x:n,y:r},c=$e(o),g=hr(c);let p=d[g],b=d[c];const h=He(i,t),v=typeof h=="number"?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){const w=g==="y"?"height":"width",C=s.reference[g]-s.floating[w]+v.mainAxis,_=s.reference[g]+s.reference[w]-v.mainAxis;p<C?p=C:p>_&&(p=_)}if(l){var x,y;const w=g==="y"?"width":"height",C=ls.has(Ge(o)),_=s.reference[c]-s.floating[w]+(C&&((x=a.offset)==null?void 0:x[c])||0)+(C?0:v.crossAxis),M=s.reference[c]+s.reference[w]+(C?0:((y=a.offset)==null?void 0:y[c])||0)-(C?v.crossAxis:0);b<_?b=_:b>M&&(b=M)}return{[g]:p,[c]:b}}}},Tl=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:a,elements:i}=t,{apply:u=()=>{},...l}=He(e,t),d=await Tt(t,l),c=Ge(o),g=Pt(o),p=$e(o)==="y",{width:b,height:h}=s.floating;let v,x;c==="top"||c==="bottom"?(v=c,x=g===(await(a.isRTL==null?void 0:a.isRTL(i.floating))?"start":"end")?"left":"right"):(x=c,v=g==="end"?"top":"bottom");const y=h-d.top-d.bottom,w=b-d.left-d.right,C=tt(h-d[v],y),_=tt(b-d[x],w),M=!t.middlewareData.shift;let E=C,R=_;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(R=w),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(E=y),M&&!g){const T=xe(d.left,0),D=xe(d.right,0),A=xe(d.top,0),G=xe(d.bottom,0);p?R=b-2*(T!==0||D!==0?T+D:xe(d.left,d.right)):E=h-2*(A!==0||G!==0?A+G:xe(d.top,d.bottom))}await u({...t,availableWidth:R,availableHeight:E});const O=await a.getDimensions(i.floating);return b!==O.width||h!==O.height?{reset:{rects:!0}}:{}}}};function vn(){return typeof window<"u"}function Mt(e){return cs(e)?(e.nodeName||"").toLowerCase():"#document"}function Ce(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Te(e){var t;return(t=(cs(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function cs(e){return vn()?e instanceof Node||e instanceof Ce(e).Node:!1}function Ne(e){return vn()?e instanceof Element||e instanceof Ce(e).Element:!1}function De(e){return vn()?e instanceof HTMLElement||e instanceof Ce(e).HTMLElement:!1}function fo(e){return!vn()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ce(e).ShadowRoot}const Vl=new Set(["inline","contents"]);function Lt(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Ae(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Vl.has(o)}const Ll=new Set(["table","td","th"]);function Bl(e){return Ll.has(Mt(e))}const zl=[":popover-open",":modal"];function xn(e){return zl.some(t=>{try{return e.matches(t)}catch{return!1}})}const Hl=["transform","translate","scale","rotate","perspective"],Gl=["transform","translate","scale","rotate","perspective","filter"],Wl=["paint","layout","strict","content"];function xr(e){const t=yr(),n=Ne(e)?Ae(e):e;return Hl.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||Gl.some(r=>(n.willChange||"").includes(r))||Wl.some(r=>(n.contain||"").includes(r))}function Ul(e){let t=nt(e);for(;De(t)&&!_t(t);){if(xr(t))return t;if(xn(t))return null;t=nt(t)}return null}function yr(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const ql=new Set(["html","body","#document"]);function _t(e){return ql.has(Mt(e))}function Ae(e){return Ce(e).getComputedStyle(e)}function yn(e){return Ne(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function nt(e){if(Mt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||fo(e)&&e.host||Te(e);return fo(t)?t.host:t}function us(e){const t=nt(e);return _t(t)?e.ownerDocument?e.ownerDocument.body:e.body:De(t)&&Lt(t)?t:us(t)}function Vt(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=us(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),a=Ce(o);if(s){const i=Zn(a);return t.concat(a,a.visualViewport||[],Lt(o)?o:[],i&&n?Vt(i):[])}return t.concat(o,Vt(o,[],n))}function Zn(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ds(e){const t=Ae(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=De(e),s=o?e.offsetWidth:n,a=o?e.offsetHeight:r,i=fn(n)!==s||fn(r)!==a;return i&&(n=s,r=a),{width:n,height:r,$:i}}function wr(e){return Ne(e)?e:e.contextElement}function wt(e){const t=wr(e);if(!De(t))return Oe(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=ds(t);let a=(s?fn(n.width):n.width)/r,i=(s?fn(n.height):n.height)/o;return(!a||!Number.isFinite(a))&&(a=1),(!i||!Number.isFinite(i))&&(i=1),{x:a,y:i}}const Xl=Oe(0);function fs(e){const t=Ce(e);return!yr()||!t.visualViewport?Xl:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Yl(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ce(e)?!1:t}function ft(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=wr(e);let a=Oe(1);t&&(r?Ne(r)&&(a=wt(r)):a=wt(e));const i=Yl(s,n,r)?fs(s):Oe(0);let u=(o.left+i.x)/a.x,l=(o.top+i.y)/a.y,d=o.width/a.x,c=o.height/a.y;if(s){const g=Ce(s),p=r&&Ne(r)?Ce(r):r;let b=g,h=Zn(b);for(;h&&r&&p!==b;){const v=wt(h),x=h.getBoundingClientRect(),y=Ae(h),w=x.left+(h.clientLeft+parseFloat(y.paddingLeft))*v.x,C=x.top+(h.clientTop+parseFloat(y.paddingTop))*v.y;u*=v.x,l*=v.y,d*=v.x,c*=v.y,u+=w,l+=C,b=Ce(h),h=Zn(b)}}return pn({width:d,height:c,x:u,y:l})}function Cr(e,t){const n=yn(e).scrollLeft;return t?t.left+n:ft(Te(e)).left+n}function gs(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Cr(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function Zl(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",a=Te(r),i=t?xn(t.floating):!1;if(r===a||i&&s)return n;let u={scrollLeft:0,scrollTop:0},l=Oe(1);const d=Oe(0),c=De(r);if((c||!c&&!s)&&((Mt(r)!=="body"||Lt(a))&&(u=yn(r)),De(r))){const p=ft(r);l=wt(r),d.x=p.x+r.clientLeft,d.y=p.y+r.clientTop}const g=a&&!c&&!s?gs(a,u,!0):Oe(0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-u.scrollLeft*l.x+d.x+g.x,y:n.y*l.y-u.scrollTop*l.y+d.y+g.y}}function Kl(e){return Array.from(e.getClientRects())}function Ql(e){const t=Te(e),n=yn(e),r=e.ownerDocument.body,o=xe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=xe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+Cr(e);const i=-n.scrollTop;return Ae(r).direction==="rtl"&&(a+=xe(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:a,y:i}}function Jl(e,t){const n=Ce(e),r=Te(e),o=n.visualViewport;let s=r.clientWidth,a=r.clientHeight,i=0,u=0;if(o){s=o.width,a=o.height;const l=yr();(!l||l&&t==="fixed")&&(i=o.offsetLeft,u=o.offsetTop)}return{width:s,height:a,x:i,y:u}}const ec=new Set(["absolute","fixed"]);function tc(e,t){const n=ft(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=De(e)?wt(e):Oe(1),a=e.clientWidth*s.x,i=e.clientHeight*s.y,u=o*s.x,l=r*s.y;return{width:a,height:i,x:u,y:l}}function go(e,t,n){let r;if(t==="viewport")r=Jl(e,n);else if(t==="document")r=Ql(Te(e));else if(Ne(t))r=tc(t,n);else{const o=fs(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return pn(r)}function ps(e,t){const n=nt(e);return n===t||!Ne(n)||_t(n)?!1:Ae(n).position==="fixed"||ps(n,t)}function nc(e,t){const n=t.get(e);if(n)return n;let r=Vt(e,[],!1).filter(i=>Ne(i)&&Mt(i)!=="body"),o=null;const s=Ae(e).position==="fixed";let a=s?nt(e):e;for(;Ne(a)&&!_t(a);){const i=Ae(a),u=xr(a);!u&&i.position==="fixed"&&(o=null),(s?!u&&!o:!u&&i.position==="static"&&!!o&&ec.has(o.position)||Lt(a)&&!u&&ps(e,a))?r=r.filter(d=>d!==a):o=i,a=nt(a)}return t.set(e,r),r}function rc(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const a=[...n==="clippingAncestors"?xn(t)?[]:nc(t,this._c):[].concat(n),r],i=a[0],u=a.reduce((l,d)=>{const c=go(t,d,o);return l.top=xe(c.top,l.top),l.right=tt(c.right,l.right),l.bottom=tt(c.bottom,l.bottom),l.left=xe(c.left,l.left),l},go(t,i,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function oc(e){const{width:t,height:n}=ds(e);return{width:t,height:n}}function sc(e,t,n){const r=De(t),o=Te(t),s=n==="fixed",a=ft(e,!0,s,t);let i={scrollLeft:0,scrollTop:0};const u=Oe(0);function l(){u.x=Cr(o)}if(r||!r&&!s)if((Mt(t)!=="body"||Lt(o))&&(i=yn(t)),r){const p=ft(t,!0,s,t);u.x=p.x+t.clientLeft,u.y=p.y+t.clientTop}else o&&l();s&&!r&&o&&l();const d=o&&!r&&!s?gs(o,i):Oe(0),c=a.left+i.scrollLeft-u.x-d.x,g=a.top+i.scrollTop-u.y-d.y;return{x:c,y:g,width:a.width,height:a.height}}function In(e){return Ae(e).position==="static"}function po(e,t){if(!De(e)||Ae(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Te(e)===n&&(n=n.ownerDocument.body),n}function ms(e,t){const n=Ce(e);if(xn(e))return n;if(!De(e)){let o=nt(e);for(;o&&!_t(o);){if(Ne(o)&&!In(o))return o;o=nt(o)}return n}let r=po(e,t);for(;r&&Bl(r)&&In(r);)r=po(r,t);return r&&_t(r)&&In(r)&&!xr(r)?n:r||Ul(e)||n}const ac=async function(e){const t=this.getOffsetParent||ms,n=this.getDimensions,r=await n(e.floating);return{reference:sc(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function ic(e){return Ae(e).direction==="rtl"}const lc={convertOffsetParentRelativeRectToViewportRelativeRect:Zl,getDocumentElement:Te,getClippingRect:rc,getOffsetParent:ms,getElementRects:ac,getClientRects:Kl,getDimensions:oc,getScale:wt,isElement:Ne,isRTL:ic};function hs(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function cc(e,t){let n=null,r;const o=Te(e);function s(){var i;clearTimeout(r),(i=n)==null||i.disconnect(),n=null}function a(i,u){i===void 0&&(i=!1),u===void 0&&(u=1),s();const l=e.getBoundingClientRect(),{left:d,top:c,width:g,height:p}=l;if(i||t(),!g||!p)return;const b=Kt(c),h=Kt(o.clientWidth-(d+g)),v=Kt(o.clientHeight-(c+p)),x=Kt(d),w={rootMargin:-b+"px "+-h+"px "+-v+"px "+-x+"px",threshold:xe(0,tt(1,u))||1};let C=!0;function _(M){const E=M[0].intersectionRatio;if(E!==u){if(!C)return a();E?a(!1,E):r=setTimeout(()=>{a(!1,1e-7)},1e3)}E===1&&!hs(l,e.getBoundingClientRect())&&a(),C=!1}try{n=new IntersectionObserver(_,{...w,root:o.ownerDocument})}catch{n=new IntersectionObserver(_,w)}n.observe(e)}return a(!0),s}function uc(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:a=typeof ResizeObserver=="function",layoutShift:i=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,l=wr(e),d=o||s?[...l?Vt(l):[],...Vt(t)]:[];d.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),s&&x.addEventListener("resize",n)});const c=l&&i?cc(l,n):null;let g=-1,p=null;a&&(p=new ResizeObserver(x=>{let[y]=x;y&&y.target===l&&p&&(p.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var w;(w=p)==null||w.observe(t)})),n()}),l&&!u&&p.observe(l),p.observe(t));let b,h=u?ft(e):null;u&&v();function v(){const x=ft(e);h&&!hs(h,x)&&n(),h=x,b=requestAnimationFrame(v)}return n(),()=>{var x;d.forEach(y=>{o&&y.removeEventListener("scroll",n),s&&y.removeEventListener("resize",n)}),c==null||c(),(x=p)==null||x.disconnect(),p=null,u&&cancelAnimationFrame(b)}}const dc=$l,fc=Ol,gc=kl,pc=Tl,mc=Il,mo=jl,hc=Dl,bc=(e,t,n)=>{const r=new Map,o={platform:lc,...n},s={...o.platform,_c:r};return Al(e,t,{...o,platform:s})};var vc=typeof document<"u",xc=function(){},cn=vc?S.useLayoutEffect:xc;function mn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!mn(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!mn(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function bs(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ho(e,t){const n=bs(e);return Math.round(t*n)/n}function Fn(e){const t=m.useRef(e);return cn(()=>{t.current=e}),t}function yc(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:a}={},transform:i=!0,whileElementsMounted:u,open:l}=e,[d,c]=m.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[g,p]=m.useState(r);mn(g,r)||p(r);const[b,h]=m.useState(null),[v,x]=m.useState(null),y=m.useCallback(j=>{j!==M.current&&(M.current=j,h(j))},[]),w=m.useCallback(j=>{j!==E.current&&(E.current=j,x(j))},[]),C=s||b,_=a||v,M=m.useRef(null),E=m.useRef(null),R=m.useRef(d),O=u!=null,T=Fn(u),D=Fn(o),A=Fn(l),G=m.useCallback(()=>{if(!M.current||!E.current)return;const j={placement:t,strategy:n,middleware:g};D.current&&(j.platform=D.current),bc(M.current,E.current,j).then($=>{const Z={...$,isPositioned:A.current!==!1};z.current&&!mn(R.current,Z)&&(R.current=Z,Fo.flushSync(()=>{c(Z)}))})},[g,t,n,D,A]);cn(()=>{l===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,c(j=>({...j,isPositioned:!1})))},[l]);const z=m.useRef(!1);cn(()=>(z.current=!0,()=>{z.current=!1}),[]),cn(()=>{if(C&&(M.current=C),_&&(E.current=_),C&&_){if(T.current)return T.current(C,_,G);G()}},[C,_,G,T,O]);const q=m.useMemo(()=>({reference:M,floating:E,setReference:y,setFloating:w}),[y,w]),N=m.useMemo(()=>({reference:C,floating:_}),[C,_]),B=m.useMemo(()=>{const j={position:n,left:0,top:0};if(!N.floating)return j;const $=ho(N.floating,d.x),Z=ho(N.floating,d.y);return i?{...j,transform:"translate("+$+"px, "+Z+"px)",...bs(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:$,top:Z}},[n,i,N.floating,d.x,d.y]);return m.useMemo(()=>({...d,update:G,refs:q,elements:N,floatingStyles:B}),[d,G,q,N,B])}const wc=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?mo({element:r.current,padding:o}).fn(n):{}:r?mo({element:r,padding:o}).fn(n):{}}}},Cc=(e,t)=>({...dc(e),options:[e,t]}),Sc=(e,t)=>({...fc(e),options:[e,t]}),_c=(e,t)=>({...hc(e),options:[e,t]}),Rc=(e,t)=>({...gc(e),options:[e,t]}),Ec=(e,t)=>({...pc(e),options:[e,t]}),Pc=(e,t)=>({...mc(e),options:[e,t]}),Mc=(e,t)=>({...wc(e),options:[e,t]});var Nc="Arrow",vs=m.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return f.jsx(be.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:f.jsx("polygon",{points:"0,0 30,0 15,10"})})});vs.displayName=Nc;var Ac=vs;function jc(e){const[t,n]=m.useState(void 0);return et(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let a,i;if("borderBoxSize"in s){const u=s.borderBoxSize,l=Array.isArray(u)?u[0]:u;a=l.inlineSize,i=l.blockSize}else a=e.offsetWidth,i=e.offsetHeight;n({width:a,height:i})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Sr="Popper",[xs,ys]=gr(Sr),[kc,ws]=xs(Sr),Cs=e=>{const{__scopePopper:t,children:n}=e,[r,o]=m.useState(null);return f.jsx(kc,{scope:t,anchor:r,onAnchorChange:o,children:n})};Cs.displayName=Sr;var Ss="PopperAnchor",_s=m.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=ws(Ss,n),a=m.useRef(null),i=je(t,a);return m.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||a.current)}),r?null:f.jsx(be.div,{...o,ref:i})});_s.displayName=Ss;var _r="PopperContent",[Ic,Fc]=xs(_r),Rs=m.forwardRef((e,t)=>{var J,re,ue,pe,_e,K;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:a=0,arrowPadding:i=0,avoidCollisions:u=!0,collisionBoundary:l=[],collisionPadding:d=0,sticky:c="partial",hideWhenDetached:g=!1,updatePositionStrategy:p="optimized",onPlaced:b,...h}=e,v=ws(_r,n),[x,y]=m.useState(null),w=je(t,le=>y(le)),[C,_]=m.useState(null),M=jc(C),E=(M==null?void 0:M.width)??0,R=(M==null?void 0:M.height)??0,O=r+(s!=="center"?"-"+s:""),T=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},D=Array.isArray(l)?l:[l],A=D.length>0,G={padding:T,boundary:D.filter(Oc),altBoundary:A},{refs:z,floatingStyles:q,placement:N,isPositioned:B,middlewareData:j}=yc({strategy:"fixed",placement:O,whileElementsMounted:(...le)=>uc(...le,{animationFrame:p==="always"}),elements:{reference:v.anchor},middleware:[Cc({mainAxis:o+R,alignmentAxis:a}),u&&Sc({mainAxis:!0,crossAxis:!1,limiter:c==="partial"?_c():void 0,...G}),u&&Rc({...G}),Ec({...G,apply:({elements:le,rects:ve,availableWidth:zt,availableHeight:We})=>{const{width:Re,height:pt}=ve.reference,de=le.floating.style;de.setProperty("--radix-popper-available-width",`${zt}px`),de.setProperty("--radix-popper-available-height",`${We}px`),de.setProperty("--radix-popper-anchor-width",`${Re}px`),de.setProperty("--radix-popper-anchor-height",`${pt}px`)}}),C&&Mc({element:C,padding:i}),Dc({arrowWidth:E,arrowHeight:R}),g&&Pc({strategy:"referenceHidden",...G})]}),[$,Z]=Ms(N),P=St(b);et(()=>{B&&(P==null||P())},[B,P]);const k=(J=j.arrow)==null?void 0:J.x,I=(re=j.arrow)==null?void 0:re.y,X=((ue=j.arrow)==null?void 0:ue.centerOffset)!==0,[te,U]=m.useState();return et(()=>{x&&U(window.getComputedStyle(x).zIndex)},[x]),f.jsx("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:B?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:te,"--radix-popper-transform-origin":[(pe=j.transformOrigin)==null?void 0:pe.x,(_e=j.transformOrigin)==null?void 0:_e.y].join(" "),...((K=j.hide)==null?void 0:K.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:f.jsx(Ic,{scope:n,placedSide:$,onArrowChange:_,arrowX:k,arrowY:I,shouldHideArrow:X,children:f.jsx(be.div,{"data-side":$,"data-align":Z,...h,ref:w,style:{...h.style,animation:B?void 0:"none"}})})})});Rs.displayName=_r;var Es="PopperArrow",$c={top:"bottom",right:"left",bottom:"top",left:"right"},Ps=m.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=Fc(Es,r),a=$c[s.placedSide];return f.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:f.jsx(Ac,{...o,ref:n,style:{...o.style,display:"block"}})})});Ps.displayName=Es;function Oc(e){return e!==null}var Dc=e=>({name:"transformOrigin",options:e,fn(t){var v,x,y;const{placement:n,rects:r,middlewareData:o}=t,a=((v=o.arrow)==null?void 0:v.centerOffset)!==0,i=a?0:e.arrowWidth,u=a?0:e.arrowHeight,[l,d]=Ms(n),c={start:"0%",center:"50%",end:"100%"}[d],g=(((x=o.arrow)==null?void 0:x.x)??0)+i/2,p=(((y=o.arrow)==null?void 0:y.y)??0)+u/2;let b="",h="";return l==="bottom"?(b=a?c:`${g}px`,h=`${-u}px`):l==="top"?(b=a?c:`${g}px`,h=`${r.floating.height+u}px`):l==="right"?(b=`${-u}px`,h=a?c:`${p}px`):l==="left"&&(b=`${r.floating.width+u}px`,h=a?c:`${p}px`),{data:{x:b,y:h}}}});function Ms(e){const[t,n="center"]=e.split("-");return[t,n]}var Tc=Cs,Ns=_s,Vc=Rs,Lc=Ps,Bc="Portal",Rr=m.forwardRef((e,t)=>{var i;const{container:n,...r}=e,[o,s]=m.useState(!1);et(()=>s(!0),[]);const a=n||o&&((i=globalThis==null?void 0:globalThis.document)==null?void 0:i.body);return a?or.createPortal(f.jsx(be.div,{...r,ref:t}),a):null});Rr.displayName=Bc;function zc(e,t){return m.useReducer((n,r)=>t[n][r]??n,e)}var Nt=e=>{const{present:t,children:n}=e,r=Hc(t),o=typeof n=="function"?n({present:r.isPresent}):m.Children.only(n),s=je(r.ref,Gc(o));return typeof n=="function"||r.isPresent?m.cloneElement(o,{ref:s}):null};Nt.displayName="Presence";function Hc(e){const[t,n]=m.useState(),r=m.useRef(null),o=m.useRef(e),s=m.useRef("none"),a=e?"mounted":"unmounted",[i,u]=zc(a,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return m.useEffect(()=>{const l=Qt(r.current);s.current=i==="mounted"?l:"none"},[i]),et(()=>{const l=r.current,d=o.current;if(d!==e){const g=s.current,p=Qt(l);e?u("MOUNT"):p==="none"||(l==null?void 0:l.display)==="none"?u("UNMOUNT"):u(d&&g!==p?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,u]),et(()=>{if(t){let l;const d=t.ownerDocument.defaultView??window,c=p=>{const h=Qt(r.current).includes(p.animationName);if(p.target===t&&h&&(u("ANIMATION_END"),!o.current)){const v=t.style.animationFillMode;t.style.animationFillMode="forwards",l=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=v)})}},g=p=>{p.target===t&&(s.current=Qt(r.current))};return t.addEventListener("animationstart",g),t.addEventListener("animationcancel",c),t.addEventListener("animationend",c),()=>{d.clearTimeout(l),t.removeEventListener("animationstart",g),t.removeEventListener("animationcancel",c),t.removeEventListener("animationend",c)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:m.useCallback(l=>{r.current=l?getComputedStyle(l):null,n(l)},[])}}function Qt(e){return(e==null?void 0:e.animationName)||"none"}function Gc(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Wc=m[" useInsertionEffect ".trim().toString()]||et;function As({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[o,s,a]=Uc({defaultProp:t,onChange:n}),i=e!==void 0,u=i?e:o;{const d=m.useRef(e!==void 0);m.useEffect(()=>{const c=d.current;c!==i&&console.warn(`${r} is changing from ${c?"controlled":"uncontrolled"} to ${i?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=i},[i,r])}const l=m.useCallback(d=>{var c;if(i){const g=qc(d)?d(e):d;g!==e&&((c=a.current)==null||c.call(a,g))}else s(d)},[i,e,s,a]);return[u,l]}function Uc({defaultProp:e,onChange:t}){const[n,r]=m.useState(e),o=m.useRef(n),s=m.useRef(t);return Wc(()=>{s.current=t},[t]),m.useEffect(()=>{var a;o.current!==n&&((a=s.current)==null||a.call(s,n),o.current=n)},[n,o]),[n,r,s]}function qc(e){return typeof e=="function"}var Xc=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ht=new WeakMap,Jt=new WeakMap,en={},$n=0,js=function(e){return e&&(e.host||js(e.parentNode))},Yc=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=js(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Zc=function(e,t,n,r){var o=Yc(t,Array.isArray(e)?e:[e]);en[n]||(en[n]=new WeakMap);var s=en[n],a=[],i=new Set,u=new Set(o),l=function(c){!c||i.has(c)||(i.add(c),l(c.parentNode))};o.forEach(l);var d=function(c){!c||u.has(c)||Array.prototype.forEach.call(c.children,function(g){if(i.has(g))d(g);else try{var p=g.getAttribute(r),b=p!==null&&p!=="false",h=(ht.get(g)||0)+1,v=(s.get(g)||0)+1;ht.set(g,h),s.set(g,v),a.push(g),h===1&&b&&Jt.set(g,!0),v===1&&g.setAttribute(n,"true"),b||g.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",g,x)}})};return d(t),i.clear(),$n++,function(){a.forEach(function(c){var g=ht.get(c)-1,p=s.get(c)-1;ht.set(c,g),s.set(c,p),g||(Jt.has(c)||c.removeAttribute(r),Jt.delete(c)),p||c.removeAttribute(n)}),$n--,$n||(ht=new WeakMap,ht=new WeakMap,Jt=new WeakMap,en={})}},ks=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Xc(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),Zc(r,o,n,"aria-hidden")):function(){return null}},Fe=function(){return Fe=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Fe.apply(this,arguments)};function Is(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function Kc(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var un="right-scroll-bar-position",dn="width-before-scroll-bar",Qc="with-scroll-bars-hidden",Jc="--removed-body-scroll-bar-size";function On(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function eu(e,t){var n=S.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var tu=typeof window<"u"?m.useLayoutEffect:m.useEffect,bo=new WeakMap;function nu(e,t){var n=eu(null,function(r){return e.forEach(function(o){return On(o,r)})});return tu(function(){var r=bo.get(n);if(r){var o=new Set(r),s=new Set(e),a=n.current;o.forEach(function(i){s.has(i)||On(i,null)}),s.forEach(function(i){o.has(i)||On(i,a)})}bo.set(n,e)},[e]),n}function ru(e){return e}function ou(e,t){t===void 0&&(t=ru);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var a=t(s,r);return n.push(a),function(){n=n.filter(function(i){return i!==a})}},assignSyncMedium:function(s){for(r=!0;n.length;){var a=n;n=[],a.forEach(s)}n={push:function(i){return s(i)},filter:function(){return n}}},assignMedium:function(s){r=!0;var a=[];if(n.length){var i=n;n=[],i.forEach(s),a=n}var u=function(){var d=a;a=[],d.forEach(s)},l=function(){return Promise.resolve().then(u)};l(),n={push:function(d){a.push(d),l()},filter:function(d){return a=a.filter(d),n}}}};return o}function su(e){e===void 0&&(e={});var t=ou(null);return t.options=Fe({async:!0,ssr:!1},e),t}var Fs=function(e){var t=e.sideCar,n=Is(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return m.createElement(r,Fe({},n))};Fs.isSideCarExport=!0;function au(e,t){return e.useMedium(t),Fs}var $s=su(),Dn=function(){},wn=m.forwardRef(function(e,t){var n=m.useRef(null),r=m.useState({onScrollCapture:Dn,onWheelCapture:Dn,onTouchMoveCapture:Dn}),o=r[0],s=r[1],a=e.forwardProps,i=e.children,u=e.className,l=e.removeScrollBar,d=e.enabled,c=e.shards,g=e.sideCar,p=e.noRelative,b=e.noIsolation,h=e.inert,v=e.allowPinchZoom,x=e.as,y=x===void 0?"div":x,w=e.gapMode,C=Is(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),_=g,M=nu([n,t]),E=Fe(Fe({},C),o);return m.createElement(m.Fragment,null,d&&m.createElement(_,{sideCar:$s,removeScrollBar:l,shards:c,noRelative:p,noIsolation:b,inert:h,setCallbacks:s,allowPinchZoom:!!v,lockRef:n,gapMode:w}),a?m.cloneElement(m.Children.only(i),Fe(Fe({},E),{ref:M})):m.createElement(y,Fe({},E,{className:u,ref:M}),i))});wn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};wn.classNames={fullWidth:dn,zeroRight:un};var iu=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function lu(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=iu();return t&&e.setAttribute("nonce",t),e}function cu(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function uu(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var du=function(){var e=0,t=null;return{add:function(n){e==0&&(t=lu())&&(cu(t,n),uu(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},fu=function(){var e=du();return function(t,n){m.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Os=function(){var e=fu(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},gu={left:0,top:0,right:0,gap:0},Tn=function(e){return parseInt(e||"",10)||0},pu=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Tn(n),Tn(r),Tn(o)]},mu=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return gu;var t=pu(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},hu=Os(),Ct="data-scroll-locked",bu=function(e,t,n,r){var o=e.left,s=e.top,a=e.right,i=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Qc,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(i,"px ").concat(r,`;
  }
  body[`).concat(Ct,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(a,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(i,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(un,` {
    right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(dn,` {
    margin-right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(un," .").concat(un,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(dn," .").concat(dn,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Ct,`] {
    `).concat(Jc,": ").concat(i,`px;
  }
`)},vo=function(){var e=parseInt(document.body.getAttribute(Ct)||"0",10);return isFinite(e)?e:0},vu=function(){m.useEffect(function(){return document.body.setAttribute(Ct,(vo()+1).toString()),function(){var e=vo()-1;e<=0?document.body.removeAttribute(Ct):document.body.setAttribute(Ct,e.toString())}},[])},xu=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;vu();var s=m.useMemo(function(){return mu(o)},[o]);return m.createElement(hu,{styles:bu(s,!t,o,n?"":"!important")})},Kn=!1;if(typeof window<"u")try{var tn=Object.defineProperty({},"passive",{get:function(){return Kn=!0,!0}});window.addEventListener("test",tn,tn),window.removeEventListener("test",tn,tn)}catch{Kn=!1}var bt=Kn?{passive:!1}:!1,yu=function(e){return e.tagName==="TEXTAREA"},Ds=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!yu(e)&&n[t]==="visible")},wu=function(e){return Ds(e,"overflowY")},Cu=function(e){return Ds(e,"overflowX")},xo=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=Ts(e,r);if(o){var s=Vs(e,r),a=s[1],i=s[2];if(a>i)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Su=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},_u=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Ts=function(e,t){return e==="v"?wu(t):Cu(t)},Vs=function(e,t){return e==="v"?Su(t):_u(t)},Ru=function(e,t){return e==="h"&&t==="rtl"?-1:1},Eu=function(e,t,n,r,o){var s=Ru(e,window.getComputedStyle(t).direction),a=s*r,i=n.target,u=t.contains(i),l=!1,d=a>0,c=0,g=0;do{if(!i)break;var p=Vs(e,i),b=p[0],h=p[1],v=p[2],x=h-v-s*b;(b||x)&&Ts(e,i)&&(c+=x,g+=b);var y=i.parentNode;i=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&i!==document.body||u&&(t.contains(i)||t===i));return(d&&Math.abs(c)<1||!d&&Math.abs(g)<1)&&(l=!0),l},nn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},yo=function(e){return[e.deltaX,e.deltaY]},wo=function(e){return e&&"current"in e?e.current:e},Pu=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Mu=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Nu=0,vt=[];function Au(e){var t=m.useRef([]),n=m.useRef([0,0]),r=m.useRef(),o=m.useState(Nu++)[0],s=m.useState(Os)[0],a=m.useRef(e);m.useEffect(function(){a.current=e},[e]),m.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var h=Kc([e.lockRef.current],(e.shards||[]).map(wo),!0).filter(Boolean);return h.forEach(function(v){return v.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),h.forEach(function(v){return v.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=m.useCallback(function(h,v){if("touches"in h&&h.touches.length===2||h.type==="wheel"&&h.ctrlKey)return!a.current.allowPinchZoom;var x=nn(h),y=n.current,w="deltaX"in h?h.deltaX:y[0]-x[0],C="deltaY"in h?h.deltaY:y[1]-x[1],_,M=h.target,E=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in h&&E==="h"&&M.type==="range")return!1;var R=xo(E,M);if(!R)return!0;if(R?_=E:(_=E==="v"?"h":"v",R=xo(E,M)),!R)return!1;if(!r.current&&"changedTouches"in h&&(w||C)&&(r.current=_),!_)return!0;var O=r.current||_;return Eu(O,v,h,O==="h"?w:C)},[]),u=m.useCallback(function(h){var v=h;if(!(!vt.length||vt[vt.length-1]!==s)){var x="deltaY"in v?yo(v):nn(v),y=t.current.filter(function(_){return _.name===v.type&&(_.target===v.target||v.target===_.shadowParent)&&Pu(_.delta,x)})[0];if(y&&y.should){v.cancelable&&v.preventDefault();return}if(!y){var w=(a.current.shards||[]).map(wo).filter(Boolean).filter(function(_){return _.contains(v.target)}),C=w.length>0?i(v,w[0]):!a.current.noIsolation;C&&v.cancelable&&v.preventDefault()}}},[]),l=m.useCallback(function(h,v,x,y){var w={name:h,delta:v,target:x,should:y,shadowParent:ju(x)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),d=m.useCallback(function(h){n.current=nn(h),r.current=void 0},[]),c=m.useCallback(function(h){l(h.type,yo(h),h.target,i(h,e.lockRef.current))},[]),g=m.useCallback(function(h){l(h.type,nn(h),h.target,i(h,e.lockRef.current))},[]);m.useEffect(function(){return vt.push(s),e.setCallbacks({onScrollCapture:c,onWheelCapture:c,onTouchMoveCapture:g}),document.addEventListener("wheel",u,bt),document.addEventListener("touchmove",u,bt),document.addEventListener("touchstart",d,bt),function(){vt=vt.filter(function(h){return h!==s}),document.removeEventListener("wheel",u,bt),document.removeEventListener("touchmove",u,bt),document.removeEventListener("touchstart",d,bt)}},[]);var p=e.removeScrollBar,b=e.inert;return m.createElement(m.Fragment,null,b?m.createElement(s,{styles:Mu(o)}):null,p?m.createElement(xu,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function ju(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ku=au($s,Au);var Er=m.forwardRef(function(e,t){return m.createElement(wn,Fe({},e,{ref:t,sideCar:ku}))});Er.classNames=wn.classNames;var Cn="Popover",[Ls,Wf]=gr(Cn,[ys]),Bt=ys(),[Iu,ot]=Ls(Cn),Bs=e=>{const{__scopePopover:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:a=!1}=e,i=Bt(t),u=m.useRef(null),[l,d]=m.useState(!1),[c,g]=As({prop:r,defaultProp:o??!1,onChange:s,caller:Cn});return f.jsx(Tc,{...i,children:f.jsx(Iu,{scope:t,contentId:ln(),triggerRef:u,open:c,onOpenChange:g,onOpenToggle:m.useCallback(()=>g(p=>!p),[g]),hasCustomAnchor:l,onCustomAnchorAdd:m.useCallback(()=>d(!0),[]),onCustomAnchorRemove:m.useCallback(()=>d(!1),[]),modal:a,children:n})})};Bs.displayName=Cn;var zs="PopoverAnchor",Fu=m.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=ot(zs,n),s=Bt(n),{onCustomAnchorAdd:a,onCustomAnchorRemove:i}=o;return m.useEffect(()=>(a(),()=>i()),[a,i]),f.jsx(Ns,{...s,...r,ref:t})});Fu.displayName=zs;var Hs="PopoverTrigger",Gs=m.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=ot(Hs,n),s=Bt(n),a=je(t,o.triggerRef),i=f.jsx(be.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Ys(o.open),...r,ref:a,onClick:we(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?i:f.jsx(Ns,{asChild:!0,...s,children:i})});Gs.displayName=Hs;var Pr="PopoverPortal",[$u,Ou]=Ls(Pr,{forceMount:void 0}),Ws=e=>{const{__scopePopover:t,forceMount:n,children:r,container:o}=e,s=ot(Pr,t);return f.jsx($u,{scope:t,forceMount:n,children:f.jsx(Nt,{present:n||s.open,children:f.jsx(Rr,{asChild:!0,container:o,children:r})})})};Ws.displayName=Pr;var Rt="PopoverContent",Us=m.forwardRef((e,t)=>{const n=Ou(Rt,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,s=ot(Rt,e.__scopePopover);return f.jsx(Nt,{present:r||s.open,children:s.modal?f.jsx(Tu,{...o,ref:t}):f.jsx(Vu,{...o,ref:t})})});Us.displayName=Rt;var Du=bn("PopoverContent.RemoveScroll"),Tu=m.forwardRef((e,t)=>{const n=ot(Rt,e.__scopePopover),r=m.useRef(null),o=je(t,r),s=m.useRef(!1);return m.useEffect(()=>{const a=r.current;if(a)return ks(a)},[]),f.jsx(Er,{as:Du,allowPinchZoom:!0,children:f.jsx(qs,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:we(e.onCloseAutoFocus,a=>{var i;a.preventDefault(),s.current||(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:we(e.onPointerDownOutside,a=>{const i=a.detail.originalEvent,u=i.button===0&&i.ctrlKey===!0,l=i.button===2||u;s.current=l},{checkForDefaultPrevented:!1}),onFocusOutside:we(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1})})})}),Vu=m.forwardRef((e,t)=>{const n=ot(Rt,e.__scopePopover),r=m.useRef(!1),o=m.useRef(!1);return f.jsx(qs,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var a,i;(a=e.onCloseAutoFocus)==null||a.call(e,s),s.defaultPrevented||(r.current||(i=n.triggerRef.current)==null||i.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var u,l;(u=e.onInteractOutside)==null||u.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const a=s.target;((l=n.triggerRef.current)==null?void 0:l.contains(a))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),qs=m.forwardRef((e,t)=>{const{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,disableOutsidePointerEvents:a,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:l,onInteractOutside:d,...c}=e,g=ot(Rt,n),p=Bt(n);return ss(),f.jsx(mr,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:f.jsx(pr,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:d,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:l,onDismiss:()=>g.onOpenChange(!1),children:f.jsx(Vc,{"data-state":Ys(g.open),role:"dialog",id:g.contentId,...p,...c,ref:t,style:{...c.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),Xs="PopoverClose",Lu=m.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=ot(Xs,n);return f.jsx(be.button,{type:"button",...r,ref:t,onClick:we(e.onClick,()=>o.onOpenChange(!1))})});Lu.displayName=Xs;var Bu="PopoverArrow",zu=m.forwardRef((e,t)=>{const{__scopePopover:n,...r}=e,o=Bt(n);return f.jsx(Lc,{...o,...r,ref:t})});zu.displayName=Bu;function Ys(e){return e?"open":"closed"}var Hu=Bs,Gu=Gs,Wu=Ws,Uu=Us;const Zs=Q("z-50 w-auto rounded-md border bg-white mt-2 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",{variants:{sectioned:{true:"p-0",false:"p-0"},fullWidth:{true:"w-full",false:"w-48"},fullHeight:{true:"h-full",false:""},fluidContent:{true:"w-auto h-auto",false:""},hideOnPrint:{true:"print:hidden",false:""}},defaultVariants:{sectioned:!1,fullWidth:!1,fullHeight:!1,fluidContent:!1,hideOnPrint:!1}}),qu=e=>{switch(e){case"above":return"top";case"below":return"bottom";case"cover":case"mostSpace":default:return"bottom"}},Xu=e=>{switch(e){case"left":return"start";case"center":return"center";case"right":return"end";default:return"center"}},Sn=m.forwardRef(({children:e,preferredPosition:t="below",preferredAlignment:n="center",active:r,activator:o,preferInputActivator:s=!0,activatorWrapper:a="div",zIndexOverride:i=400,preventFocusOnClose:u=!1,sectioned:l=!1,fullWidth:d=!1,fullHeight:c=!1,fluidContent:g=!1,fixed:p=!1,ariaHaspopup:b,hideOnPrint:h=!1,onClose:v,autofocusTarget:x="container",preventCloseOnChildOverlayClick:y=!1,captureOverscroll:w=!1,className:C,..._},M)=>{const E=z=>{z||v("click-outside")},R=()=>{v("escape")},O=z=>{y||v("click-outside")},T=F(Zs({sectioned:l,fullWidth:d,fullHeight:c,fluidContent:g,hideOnPrint:h}),C),D=qu(t),A=Xu(n),G=a;return f.jsxs(Hu,{open:r,onOpenChange:E,children:[f.jsx(Gu,{asChild:!0,children:f.jsx(G,{children:o})}),f.jsx(Wu,{children:f.jsx(Uu,{ref:M,side:D,align:A,className:T,style:{zIndex:i},onEscapeKeyDown:R,onInteractOutside:O,..._,children:l?f.jsx("div",{className:"p-4",children:e}):e})})]})});Sn.displayName="Popover";const Ks=Q("space-y-1",{variants:{tone:{magic:"border-l-4 border-purple-500 pl-4",default:""},disabled:{true:"opacity-50 pointer-events-none",false:""}},defaultVariants:{tone:"default",disabled:!1}}),Yu=Q("flex items-center justify-center gap-2 rounded-lg transition-colors",{variants:{disabled:{true:"opacity-50 cursor-not-allowed",false:"cursor-pointer"},selected:{true:"border-black",false:""}},defaultVariants:{disabled:!1,selected:!1}}),Mr=m.forwardRef(({title:e,choices:t,selected:n,name:r="choiceList",allowMultiple:o=!1,titleHidden:s=!1,error:a,disabled:i=!1,onChange:u,tone:l,className:d,asChild:c=!1,...g},p)=>{const b=(x,y)=>{if(i)return;let w;o?y?w=[...n,x]:w=n.filter(C=>C!==x):w=y?[x]:[],u&&u(w,r)},h=()=>{i||u&&u([],r)},v=F(Ks({tone:l==="magic"?"magic":"default",disabled:i}),d);return f.jsxs("fieldset",{ref:p,className:v,disabled:i,...g,children:[!s&&f.jsx("legend",{className:"text-sm font-medium text-gray-900 mb-3",children:e}),f.jsx(f.Fragment,{children:t.map((x,y)=>{const w=n.includes(x.value),C=`${r}-${x.value}-${y}`,_=o?"checkbox":"radio";return f.jsxs("label",{className:F(Yu({disabled:i||x.disabled,selected:w})),children:[f.jsx("input",{type:_,id:C,name:o?`${r}[]`:r,value:x.value,checked:w,disabled:i||x.disabled,onChange:M=>b(x.value,M.target.checked),className:"mt-0.5 h-4 w-4 text-black rounded accent-black cursor-pointer"}),f.jsxs("div",{className:"flex-1",children:[f.jsx(ye,{variant:"bodyMd",as:"span",children:x.label}),x.helpText&&f.jsx(ye,{variant:"bodySm",as:"p",tone:"subdued",children:x.helpText})]})]},x.value)})}),n.length>0&&f.jsx(oe,{variant:"plain",onClick:h,disabled:i,children:"Clear"}),a&&f.jsx(ye,{variant:"bodySm",tone:"critical",as:"p",className:"mt-2",children:a})]})});Mr.displayName="ChoiceList";const Zu=e=>e&&{bg:"bg-gray-50","bg-inverse":"bg-gray-900","bg-surface":"bg-white","bg-surface-hover":"bg-gray-50","bg-surface-active":"bg-gray-100","bg-surface-selected":"bg-blue-50","bg-surface-disabled":"bg-gray-100","bg-surface-secondary":"bg-gray-25","bg-surface-secondary-hover":"bg-gray-50","bg-surface-secondary-active":"bg-gray-100","bg-surface-secondary-selected":"bg-blue-50","bg-surface-tertiary":"bg-gray-100","bg-surface-tertiary-hover":"bg-gray-200","bg-surface-tertiary-active":"bg-gray-300","bg-surface-brand":"bg-gray-900","bg-surface-brand-hover":"bg-gray-800","bg-surface-brand-active":"bg-gray-700","bg-surface-brand-selected":"bg-gray-800","bg-surface-info":"bg-blue-50","bg-surface-info-hover":"bg-blue-100","bg-surface-info-active":"bg-blue-200","bg-surface-success":"bg-green-50","bg-surface-success-hover":"bg-green-100","bg-surface-success-active":"bg-green-200","bg-surface-caution":"bg-yellow-50","bg-surface-caution-hover":"bg-yellow-100","bg-surface-caution-active":"bg-yellow-200","bg-surface-warning":"bg-orange-50","bg-surface-warning-hover":"bg-orange-100","bg-surface-warning-active":"bg-orange-200","bg-surface-critical":"bg-red-50","bg-surface-critical-hover":"bg-red-100","bg-surface-critical-active":"bg-red-200","bg-surface-emphasis":"bg-blue-600","bg-surface-emphasis-hover":"bg-blue-700","bg-surface-emphasis-active":"bg-blue-800","bg-surface-magic":"bg-purple-50","bg-surface-magic-hover":"bg-purple-100","bg-surface-magic-active":"bg-purple-200","bg-surface-inverse":"bg-gray-900","bg-surface-transparent":"bg-transparent","bg-surface-subdued":"bg-gray-25","bg-surface-neutral":"bg-gray-50","bg-surface-neutral-subdued":"bg-gray-100","bg-surface-caution-subdued":"bg-yellow-25","bg-surface-warning-subdued":"bg-orange-25","bg-surface-critical-subdued":"bg-red-25","bg-surface-success-subdued":"bg-green-25","bg-surface-highlight":"bg-blue-50","bg-surface-highlight-subdued":"bg-blue-25","bg-surface-magic-subdued":"bg-purple-25","bg-surface-info-subdued":"bg-blue-25","bg-surface-brand-subdued":"bg-gray-800"}[e]||"bg-white",Qs=Q("rounded-lg border text-gray-900",{variants:{roundedAbove:{xs:"rounded-lg",sm:"sm:rounded-lg",md:"md:rounded-lg",lg:"lg:rounded-lg",xl:"xl:rounded-lg"}},defaultVariants:{roundedAbove:"sm"}}),Ku=e=>{if(!e)return"p-4 sm:p-5";if(typeof e=="string")return{0:"p-0","025":"p-0.5","050":"p-1",100:"p-2",150:"p-3",200:"p-4",300:"p-6",400:"p-4",500:"p-5",600:"p-6",800:"p-8",1e3:"p-10",1200:"p-12",1600:"p-16",2e3:"p-20",2400:"p-24",2800:"p-28",3200:"p-32"}[e]||"p-4";const t=[],n={xs:"",sm:"sm:",md:"md:",lg:"lg:",xl:"xl:"};return Object.entries(e).forEach(([r,o])=>{var i;const s=n[r],a={0:"p-0","025":"p-0.5","050":"p-1",100:"p-2",150:"p-3",200:"p-4",300:"p-6",400:"p-4",500:"p-5",600:"p-6",800:"p-8",1e3:"p-10",1200:"p-12",1600:"p-16",2e3:"p-20",2400:"p-24",2800:"p-28",3200:"p-32"};o&&t.push(`${s}${((i=a[o])==null?void 0:i.replace("p-","p-"))||"p-4"}`)}),t.join(" ")||"p-4 sm:p-5"},Js=m.forwardRef(({children:e,background:t="bg-surface",padding:n,roundedAbove:r="sm",className:o,...s},a)=>{const i=F(Qs({roundedAbove:r}),Zu(t),Ku(n),"[border-color:rgba(227,227,227,1)]","[box-shadow:0px_4px_6px_-2px_rgba(26,26,26,0.20)]","[color:rgba(48,48,48,1)]",o);return f.jsx("div",{ref:a,className:i,...s,children:e})});Js.displayName="Card";var _n="Dialog",[ea,Uf]=gr(_n),[Qu,ke]=ea(_n),ta=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:a=!0}=e,i=m.useRef(null),u=m.useRef(null),[l,d]=As({prop:r,defaultProp:o??!1,onChange:s,caller:_n});return f.jsx(Qu,{scope:t,triggerRef:i,contentRef:u,contentId:ln(),titleId:ln(),descriptionId:ln(),open:l,onOpenChange:d,onOpenToggle:m.useCallback(()=>d(c=>!c),[d]),modal:a,children:n})};ta.displayName=_n;var na="DialogTrigger",Ju=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=ke(na,n),s=je(t,o.triggerRef);return f.jsx(be.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":jr(o.open),...r,ref:s,onClick:we(e.onClick,o.onOpenToggle)})});Ju.displayName=na;var Nr="DialogPortal",[ed,ra]=ea(Nr,{forceMount:void 0}),oa=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=ke(Nr,t);return f.jsx(ed,{scope:t,forceMount:n,children:m.Children.map(r,a=>f.jsx(Nt,{present:n||s.open,children:f.jsx(Rr,{asChild:!0,container:o,children:a})}))})};oa.displayName=Nr;var hn="DialogOverlay",sa=m.forwardRef((e,t)=>{const n=ra(hn,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=ke(hn,e.__scopeDialog);return s.modal?f.jsx(Nt,{present:r||s.open,children:f.jsx(nd,{...o,ref:t})}):null});sa.displayName=hn;var td=bn("DialogOverlay.RemoveScroll"),nd=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=ke(hn,n);return f.jsx(Er,{as:td,allowPinchZoom:!0,shards:[o.contentRef],children:f.jsx(be.div,{"data-state":jr(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),gt="DialogContent",aa=m.forwardRef((e,t)=>{const n=ra(gt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,s=ke(gt,e.__scopeDialog);return f.jsx(Nt,{present:r||s.open,children:s.modal?f.jsx(rd,{...o,ref:t}):f.jsx(od,{...o,ref:t})})});aa.displayName=gt;var rd=m.forwardRef((e,t)=>{const n=ke(gt,e.__scopeDialog),r=m.useRef(null),o=je(t,n.contentRef,r);return m.useEffect(()=>{const s=r.current;if(s)return ks(s)},[]),f.jsx(ia,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:we(e.onCloseAutoFocus,s=>{var a;s.preventDefault(),(a=n.triggerRef.current)==null||a.focus()}),onPointerDownOutside:we(e.onPointerDownOutside,s=>{const a=s.detail.originalEvent,i=a.button===0&&a.ctrlKey===!0;(a.button===2||i)&&s.preventDefault()}),onFocusOutside:we(e.onFocusOutside,s=>s.preventDefault())})}),od=m.forwardRef((e,t)=>{const n=ke(gt,e.__scopeDialog),r=m.useRef(!1),o=m.useRef(!1);return f.jsx(ia,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var a,i;(a=e.onCloseAutoFocus)==null||a.call(e,s),s.defaultPrevented||(r.current||(i=n.triggerRef.current)==null||i.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var u,l;(u=e.onInteractOutside)==null||u.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const a=s.target;((l=n.triggerRef.current)==null?void 0:l.contains(a))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}})}),ia=m.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s,...a}=e,i=ke(gt,n),u=m.useRef(null),l=je(t,u);return ss(),f.jsxs(f.Fragment,{children:[f.jsx(mr,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:f.jsx(pr,{role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":jr(i.open),...a,ref:l,onDismiss:()=>i.onOpenChange(!1)})}),f.jsxs(f.Fragment,{children:[f.jsx(sd,{titleId:i.titleId}),f.jsx(id,{contentRef:u,descriptionId:i.descriptionId})]})]})}),Ar="DialogTitle",la=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=ke(Ar,n);return f.jsx(be.h2,{id:o.titleId,...r,ref:t})});la.displayName=Ar;var ca="DialogDescription",ua=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=ke(ca,n);return f.jsx(be.p,{id:o.descriptionId,...r,ref:t})});ua.displayName=ca;var da="DialogClose",fa=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=ke(da,n);return f.jsx(be.button,{type:"button",...r,ref:t,onClick:we(e.onClick,()=>o.onOpenChange(!1))})});fa.displayName=da;function jr(e){return e?"open":"closed"}var ga="DialogTitleWarning",[qf,pa]=Qi(ga,{contentName:gt,titleName:Ar,docsSlug:"dialog"}),sd=({titleId:e})=>{const t=pa(ga),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return m.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},ad="DialogDescriptionWarning",id=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${pa(ad).contentName}}.`;return m.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},ma=ta,ha=oa,kr=sa,Ir=aa,ba=la,va=ua,ld=fa;function cd({...e}){return f.jsx(ma,{"data-slot":"dialog",...e})}function xa({...e}){return f.jsx(ha,{"data-slot":"dialog-portal",...e})}function ya({className:e,...t}){return f.jsx(kr,{"data-slot":"dialog-overlay",className:F("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function ud({className:e,children:t,showCloseButton:n=!0,...r}){return f.jsxs(xa,{"data-slot":"dialog-portal",children:[f.jsx(ya,{}),f.jsxs(Ir,{"data-slot":"dialog-content",className:F("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,n&&f.jsxs(ld,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[f.jsx(cr,{}),f.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}const wa=Q("bg-white data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%] gap-0 rounded-lg border border-gray-200 shadow-lg duration-200 font-inter",{variants:{variant:{small:"!max-w-sm",base:"!max-w-lg",large:"!max-w-2xl",max:"max-w-[90vw] max-h-[90vh]"}},defaultVariants:{variant:"base"}}),Ca=m.forwardRef(({children:e,src:t,variant:n="base",open:r,onShow:o,onHide:s,onOpenChange:a,className:i,showCloseButton:u=!0,actions:l,...d},c)=>{const g=m.useCallback(b=>{b&&o?o():!b&&s&&s(),a&&a(b)},[o,s,a]),p=()=>t?f.jsxs("div",{className:"flex flex-col h-full",children:[f.jsx("iframe",{src:t,className:"w-full flex-1 min-h-[400px] border-0",title:`Modal content from ${t}`,...d}),l&&f.jsx("div",{className:"border-t border-gray-200 px-6 py-4 bg-white flex justify-end gap-2",children:l})]}):f.jsxs("div",{className:"flex flex-col h-full",children:[f.jsx("div",{className:"flex-1",children:e}),l&&f.jsx("div",{className:"border-t border-gray-200 px-6 py-4 bg-white flex justify-end gap-2",children:l})]});return f.jsx(cd,{open:r,onOpenChange:g,children:f.jsxs(xa,{children:[f.jsx(ya,{className:"bg-black/50"}),f.jsx(ud,{ref:c,className:F(wa({variant:n}),"p-0 overflow-hidden",i),showCloseButton:u,...d,children:p()})]})})});Ca.displayName="Modal";const Sa=Q("flex items-center justify-between px-6 py-4 bg-gray-100 font-inter",{variants:{showBorder:{true:"border-b border-gray-200",false:""}},defaultVariants:{showBorder:!0}}),dd=Q("text-lg font-semibold text-gray-900 leading-6"),fd=Q("flex items-center gap-2 cursor-pointer"),_a=m.forwardRef(({title:e,children:t,className:n,showBorder:r=!0,...o},s)=>f.jsxs("div",{ref:s,className:F(Sa({showBorder:r}),n),...o,children:[e&&f.jsx("h2",{className:F(dd()),children:e}),t&&f.jsx("div",{className:F(fd()),children:t}),!e&&t&&f.jsx("div",{className:"flex-1"})]}));_a.displayName="TitleBar";const Co=e=>e&&{0:"0","025":"px","050":"0.5",100:"1",150:"1.5",200:"2",300:"3",400:"4",500:"5",600:"6",800:"8",1e3:"10",1200:"12",1600:"16",2e3:"20",2400:"24",2800:"28",3200:"32"}[e]||"",xt=(e,t="m")=>{if(!e)return"";if(typeof e=="string"){const r=Co(e);return r?`-m${t}-${r}`:""}const n=[];return Object.entries(e).forEach(([r,o])=>{const s=Co(o);if(s){const a=r==="xs"?"":`${r}:`;n.push(`${a}-m${t}-${s}`)}}),n.join(" ")},Ra=Q("",{variants:{},defaultVariants:{}}),Ea=m.forwardRef(({children:e,marginInline:t,marginBlock:n,marginBlockStart:r,marginBlockEnd:o,marginInlineStart:s,marginInlineEnd:a,className:i,...u},l)=>{const d=F(Ra(),xt(t,"x"),xt(n,"y"),xt(r,"t"),xt(o,"b"),xt(s,"l"),xt(a,"r"),i);return f.jsx("div",{ref:l,className:d,...u,children:e})});Ea.displayName="Bleed";const Pa=Q("min-h-screen",{variants:{width:{default:"max-w-[1200px] mx-auto",full:"w-full",narrow:"max-w-[760px] mx-auto"}},defaultVariants:{width:"default"}}),gd=Q("bg-white border-b border-[rgb(227,227,227)] px-6 py-4",{variants:{compact:{true:"pb-2",false:"pb-4"}},defaultVariants:{compact:!1}}),pd=Q("px-6 py-6",{variants:{},defaultVariants:{}}),Ma=m.forwardRef(({children:e,title:t,subtitle:n,titleMetadata:r,compactTitle:o=!1,titleHidden:s=!1,fullWidth:a=!1,narrowWidth:i=!1,primaryAction:u,secondaryActions:l=[],actionGroups:d=[],backAction:c,pagination:g,pageReadyAccessibilityLabel:p,filterActions:b=!1,additionalMetadata:h,onActionRollup:v,hasSubtitleMaxWidth:x=!1,className:y,...w},C)=>{const _=a?"full":i?"narrow":"default",M=(R,O="secondary")=>{var D;const T="url"in R&&R.url;return f.jsx(oe,{variant:O==="primary"?"primary":"secondary",onClick:R.onAction,url:T?R.url:void 0,external:"external"in R?R.external:void 0,target:"target"in R?R.target:void 0,download:"download"in R?R.download:void 0,disabled:"disabled"in R?R.disabled:void 0,loading:"loading"in R?R.loading:void 0,icon:"icon"in R?R.icon:void 0,accessibilityLabel:R.accessibilityLabel,children:R.content},R.id||((D=R.content)==null?void 0:D.toString()))},E=()=>{var R,O;return g?f.jsxs("div",{className:"flex items-center justify-between px-6 py-4 bg-white border-t border-[rgb(227,227,227)]",children:[f.jsx(oe,{variant:"secondary",disabled:!g.hasPrevious,onClick:g.onPrevious,url:g.previousURL,icon:f.jsx(ar,{}),accessibilityLabel:((R=g.accessibilityLabels)==null?void 0:R.previous)||"Previous",children:"Previous"}),f.jsx(oe,{variant:"secondary",disabled:!g.hasNext,onClick:g.onNext,url:g.nextURL,icon:f.jsx(ir,{}),accessibilityLabel:((O=g.accessibilityLabels)==null?void 0:O.next)||"Next",children:"Next"})]}):null};return f.jsxs("div",{ref:C,className:F(Pa({width:_}),y),"aria-label":p,...w,children:[(t||c||u||l.length>0||d.length>0)&&f.jsxs("header",{className:F(gd({compact:o})),children:[c&&f.jsx("div",{className:"mb-4",children:M(c,"secondary")}),(t||u||l.length>0||d.length>0)&&f.jsxs("div",{className:"flex items-start justify-between",children:[f.jsxs("div",{className:"flex-1 min-w-0",children:[t&&!s&&f.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[f.jsx(ye,{as:"h1",variant:"headingLg",fontWeight:"semibold",children:t}),r&&f.jsx("div",{className:"flex items-center",children:r})]}),n&&f.jsx("div",{className:F("mt-1",x&&"max-w-[640px]"),children:f.jsx(ye,{variant:"bodyMd",tone:"subdued",children:n})}),h&&f.jsx("div",{className:"mt-2",children:h})]}),(u||l.length>0||d.length>0)&&f.jsx("div",{className:"flex items-center gap-2 ml-4",children:f.jsxs(dr,{children:[l.map(R=>M(R,"secondary")),u&&M(u,"primary")]})})]})]}),f.jsx("main",{className:F(pd()),children:e}),E()]})});Ma.displayName="Page";const Na=Q("inline-flex items-center justify-center whitespace-nowrap font-sans text-xs font-medium leading-none rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{tone:{default:"bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200",success:"bg-green-100 text-green-800 border-green-200 hover:bg-green-200",critical:"bg-red-100 text-red-800 border-red-200 hover:bg-red-200",warning:"bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200",attention:"bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200",info:"bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200",new:"bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200","read-only":"bg-gray-50 text-gray-600 border-gray-100 hover:bg-gray-100",enabled:"bg-emerald-100 text-emerald-800 border-emerald-200 hover:bg-emerald-200",subdued:"bg-gray-50 text-gray-500 border-gray-100 hover:bg-gray-100"},size:{small:"h-5 px-2 text-xs",medium:"h-6 px-2.5 text-xs"},progress:{incomplete:"opacity-60",partiallyComplete:"opacity-80",complete:"opacity-100"}},defaultVariants:{tone:"default",size:"medium",progress:"complete"}}),md=Na,Aa=m.forwardRef(({children:e,tone:t="default",progress:n="complete",icon:r,size:o="medium",className:s,asChild:a=!1,...i},u)=>{const l=F(Na({tone:t,size:o,progress:n}),s),d=m.useMemo(()=>{var c;if(!r)return null;if(typeof r=="string")return r==="placeholder"?f.jsx("span",{className:"w-3 h-3 bg-current rounded-full opacity-50 mr-1"}):f.jsx("span",{className:"mr-1",children:r});if(m.isValidElement(r))return m.cloneElement(r,{className:F("w-3 h-3 mr-1",(c=r.props)==null?void 0:c.className)});if(typeof r=="function"){const g=r;return f.jsx(g,{className:"w-3 h-3 mr-1"})}return null},[r]);return f.jsxs("span",{ref:u,className:l,...i,children:[d,e]})});Aa.displayName="Badge";const So=e=>e&&{0:"0","025":"px","050":"0.5",100:"1",150:"1.5",200:"2",300:"3",400:"4",500:"5",600:"6",800:"8",1e3:"10",1200:"12",1600:"16",2e3:"20",2400:"24",2800:"28",3200:"32"}[e]||"",hd=e=>{if(!e)return"";if(typeof e=="string"){const n=So(e);return n?`gap-${n}`:""}const t=[];return Object.entries(e).forEach(([n,r])=>{const o=So(r);if(o){const s=n==="xs"?"":`${n}:`;t.push(`${s}gap-${o}`)}}),t.join(" ")},ja=Q("flex",{variants:{align:{start:"justify-start",center:"justify-center",end:"justify-end","space-around":"justify-around","space-between":"justify-between","space-evenly":"justify-evenly"},direction:{row:"flex-row","row-reverse":"flex-row-reverse"},blockAlign:{start:"items-start",center:"items-center",end:"items-end",baseline:"items-baseline",stretch:"items-stretch"},wrap:{true:"flex-wrap",false:"flex-nowrap"}},defaultVariants:{align:"start",direction:"row",blockAlign:"start",wrap:!0}}),ka=m.forwardRef(({children:e,as:t="div",align:n="start",direction:r="row",blockAlign:o="start",gap:s,wrap:a=!0,className:i,...u},l)=>{const d=t,c=hd(s),g=F(ja({align:n,direction:r,blockAlign:o,wrap:a}),c,i);return f.jsx(d,{ref:l,className:g,...u,children:e})});ka.displayName="InlineStack";const _o=e=>e&&{0:"0","025":"px","050":"0.5",100:"1",150:"1.5",200:"2",300:"3",400:"4",500:"5",600:"6",800:"8",1e3:"10",1200:"12",1600:"16",2e3:"20",2400:"24",2800:"28",3200:"32"}[e]||"",bd=e=>{if(!e)return"";if(typeof e=="string"){const n=_o(e);return n?`gap-${n}`:""}const t=[];return Object.entries(e).forEach(([n,r])=>{const o=_o(r);if(o){const s=n==="xs"?"":`${n}:`;t.push(`${s}gap-${o}`)}}),t.join(" ")},Ia=Q("flex flex-col",{variants:{align:{start:"justify-start",center:"justify-center",end:"justify-end","space-around":"justify-around","space-between":"justify-between","space-evenly":"justify-evenly"},inlineAlign:{start:"items-start",center:"items-center",end:"items-end",baseline:"items-baseline",stretch:"items-stretch"},reverseOrder:{true:"flex-col-reverse",false:"flex-col"}},defaultVariants:{align:"start",inlineAlign:"start",reverseOrder:!1}}),Fa=m.forwardRef(({children:e,as:t="div",align:n="start",inlineAlign:r="start",gap:o,id:s,reverseOrder:a=!1,role:i,className:u,...l},d)=>{const c=t,g=bd(o),p=F(Ia({align:n,inlineAlign:r,reverseOrder:a}),g,u);return f.jsx(c,{ref:d,className:p,id:s,role:i,...l,children:e})});Fa.displayName="BlockStack";const $a=Q("flex items-center",{variants:{type:{page:"w-full",table:"w-auto"},hasLabel:{true:"justify-between",false:"justify-center"}},defaultVariants:{type:"page",hasLabel:!1}}),Ro=Q("inline-flex items-center h-6 justify-center cursor-pointer gap-1 p-1 text-sm font-medium bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white first:rounded-l-md last:rounded-r-md",{variants:{position:{previous:"rounded-l-md border-r-0",next:"rounded-r-md",single:""},disabled:{true:"text-gray-400 bg-gray-50",false:""}},defaultVariants:{position:"single",disabled:!1}}),Fr=m.forwardRef(({nextKeys:e,previousKeys:t,nextTooltip:n,previousTooltip:r,nextURL:o,previousURL:s,hasNext:a=!1,hasPrevious:i=!1,accessibilityLabel:u="Pagination",accessibilityLabels:l,onNext:d,onPrevious:c,label:g,type:p="page",className:b,asChild:h=!1,...v},x)=>{m.useEffect(()=>{const M=E=>{t!=null&&t.includes(E.key)&&i&&c&&(E.preventDefault(),c()),e!=null&&e.includes(E.key)&&a&&d&&(E.preventDefault(),d())};return document.addEventListener("keydown",M),()=>document.removeEventListener("keydown",M)},[t,e,i,a,c,d]);const y=l==null?void 0:l.previous,w=l==null?void 0:l.next,C=()=>f.jsx("button",{type:"button",disabled:!i,onClick:c,"aria-label":y||"Previous",className:F(Ro({position:g?"previous":"single",disabled:!i})),children:f.jsx(ar,{className:"size-5"})}),_=()=>f.jsx("button",{type:"button",disabled:!a,onClick:d,"aria-label":w||"Next",className:F(Ro({position:g?"next":"single",disabled:!a})),children:f.jsx(ir,{className:"size-5"})});return f.jsx("nav",{ref:x,"aria-label":u,className:F($a({type:p,hasLabel:!!g}),b),...v,children:g?f.jsxs(f.Fragment,{children:[f.jsx(C,{}),f.jsx("div",{className:"flex-1 flex justify-center gap-2",children:typeof g=="string"?f.jsx(ye,{className:"mx-1",variant:"bodySm",tone:"subdued",children:g}):g}),f.jsx(_,{})]}):f.jsxs("div",{className:"inline-flex",children:[f.jsx(C,{}),f.jsx(_,{})]})})});Fr.displayName="Pagination";const Oa=Q("w-full",{variants:{mode:{DEFAULT:"",FILTERING:""},disabled:{true:"opacity-50 pointer-events-none",false:""}},defaultVariants:{mode:"DEFAULT",disabled:!1}}),vd=()=>{const[e,t]=m.useState("DEFAULT");return{mode:e,setMode:t}},xd=({filter:e,disabled:t=!1,isOpen:n,onToggle:r,onClose:o})=>{const s=a=>{e.onChange(a,e.key)};return f.jsx(Sn,{active:n,activator:f.jsxs(oe,{size:"micro",variant:"tertiary",className:"border-dashed ml-2",onClick:r,disabled:t,pressed:n,children:[e.label,e.selected.length>0&&f.jsx("span",{className:"ml-1 px-1.5 py-0.5 text-xs bg-blue-100 text-black rounded-full",children:e.selected.length})]}),onClose:()=>{},preferredPosition:"below",sectioned:!0,children:f.jsx(Mr,{title:e.label,titleHidden:!0,choices:e.choices,selected:e.selected,onChange:s,allowMultiple:e.allowMultiple})})},Eo=({sortOptions:e,onSort:t,disabled:n,isOpen:r,onToggle:o,onClose:s})=>f.jsx(Sn,{active:r,activator:f.jsx(oe,{variant:"tertiary",icon:f.jsx(Go,{className:"size-4"}),disclosure:!0,disabled:n,onClick:o,children:"Sort"}),onClose:s,children:f.jsx("div",{className:"p-2 min-w-48",children:e.map(a=>f.jsxs("button",{className:"w-full text-left px-3 py-2 hover:bg-[#f6f6f6] rounded text-sm",onClick:()=>{t&&t([a.value]),s()},children:[a.label," (",a.directionLabel,")"]},a.value))})}),Da=m.forwardRef(({sortOptions:e,sortSelected:t,onSort:n,onSortKeyChange:r,onSortDirectionChange:o,onAddFilterClick:s,primaryAction:a,cancelAction:i,onEditStart:u,mode:l,disclosureZIndexOverride:d,setMode:c,disabled:g=!1,disableQueryField:p=!1,disableStickyMode:b,isFlushWhenSticky:h,canCreateNewView:v,onCreateNewView:x,filteringAccessibilityLabel:y="Filter",filteringAccessibilityTooltip:w,closeOnChildOverlayClick:C,disableKeyboardShortcuts:_,showEditColumnsButton:M=!1,autoFocusSearchField:E=!1,tabs:R,selected:O=0,onSelect:T,filters:D,appliedFilters:A,onClearAll:G,pinnedFilters:z,queryValue:q="",queryPlaceholder:N="Searching in all..",onQueryChange:B,onQueryClear:j,className:$,asChild:Z=!1,...P},k)=>{const[I,X]=m.useState(l==="FILTERING"),[te,U]=m.useState(null);m.useEffect(()=>{X(l==="FILTERING")},[l]);const J=K=>{U(le=>le===K?null:K)},re=()=>{U(null)},ue=()=>{c(I?"DEFAULT":"FILTERING"),X(!I),!I&&u&&u("FILTERING")},pe=K=>{B&&B(K)},_e=()=>{j&&j()};return f.jsx("div",{ref:k,className:F(Oa({mode:l,disabled:g}),$),...P,children:f.jsx("div",{className:"p-1 border border-[#e3e3e3] rounded-md border-b-0 rounded-b-none",children:I?f.jsxs("div",{className:"space-y-2",children:[f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("div",{className:"flex items-center gap-2",children:e&&e.length>0&&f.jsx(Eo,{sortOptions:e,onSort:n,disabled:g,isOpen:te==="sort",onToggle:()=>J("sort"),onClose:re})}),f.jsx("div",{className:"flex-1",children:f.jsx(fr,{label:"",labelHidden:!0,value:q,onChange:pe,placeholder:N,clearButton:q.length>0,onClearButtonClick:_e,disabled:p||g,autoFocus:E,autoComplete:"off",size:"slim",className:"border border-[#e3e3e3] hover:border-[#e3e3e3] bg-white"})}),f.jsx(oe,{onClick:ue,disabled:g,size:"medium",children:"Cancel"})]}),z&&z.length>0&&f.jsx("div",{className:"border-t border-gray-200 pt-0.5",children:f.jsx("div",{className:"flex flex-wrap gap-2",children:z.map(K=>f.jsx(xd,{filter:K,disabled:g,isOpen:te===`filter-${K.key}`,onToggle:()=>J(`filter-${K.key}`),onClose:re},K.key))})}),A&&A.length>0&&f.jsxs("div",{className:"flex flex-wrap gap-2",children:[A.map(K=>f.jsxs("div",{className:"inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-black rounded-md text-sm",children:[f.jsx(ye,{variant:"bodySm",children:K.label}),f.jsx("button",{onClick:K.onRemove,className:"p-0.5 hover:bg-black rounded",children:f.jsx(cr,{className:"size-3"})})]},K.key)),G&&f.jsx(oe,{variant:"plain",size:"micro",onClick:G,children:"Clear all"})]}),(a||i)&&f.jsxs("div",{className:"flex justify-end gap-2",children:[i&&f.jsx(oe,{variant:"secondary",onClick:i.onAction,disabled:i.disabled,loading:i.loading,children:"Cancel"}),a&&f.jsx(oe,{variant:"primary",onClick:()=>{if(a.type==="save-as"&&x){const K=prompt("Enter view name:");K&&a.onAction(K)}else a.onAction()},disabled:a.disabled,loading:a.loading,children:a.type==="save"?"Save":"Save as"})]})]}):f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{className:"flex items-center gap-2",children:[e&&e.length>0&&f.jsx(Eo,{sortOptions:e,onSort:n,disabled:g,isOpen:te==="sort",onToggle:()=>J("sort"),onClose:re}),R&&R.length>0&&f.jsx(f.Fragment,{children:R.map((K,le)=>f.jsx(oe,{size:"micro",variant:"tertiary",pressed:le===O,onClick:()=>{K.onAction(),T&&T(le)},disabled:g,children:K.content},K.id))})]}),f.jsxs("div",{className:"flex items-center gap-2",children:[M&&f.jsx(oe,{variant:"tertiary",icon:f.jsx(Ho,{className:"size-4"}),accessibilityLabel:"Edit columns",disabled:g,children:"Edit columns"}),f.jsx(oe,{variant:"tertiary",className:"flex w-12 justify-items-end",icon:f.jsxs(f.Fragment,{children:[f.jsx(zo,{className:"size-5"}),f.jsx(Lo,{className:"size-5"})]}),onClick:ue,pressed:!1,accessibilityLabel:y,disabled:g})]})]})})})});Da.displayName="IndexFilters";/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Je(e,t){return typeof e=="function"?e(t):e}function Se(e,t){return n=>{t.setState(r=>({...r,[e]:Je(n,r[e])}))}}function Rn(e){return e instanceof Function}function yd(e){return Array.isArray(e)&&e.every(t=>typeof t=="number")}function wd(e,t){const n=[],r=o=>{o.forEach(s=>{n.push(s);const a=t(s);a!=null&&a.length&&r(a)})};return r(e),n}function V(e,t,n){let r=[],o;return s=>{let a;n.key&&n.debug&&(a=Date.now());const i=e(s);if(!(i.length!==r.length||i.some((d,c)=>r[c]!==d)))return o;r=i;let l;if(n.key&&n.debug&&(l=Date.now()),o=t(...i),n==null||n.onChange==null||n.onChange(o),n.key&&n.debug&&n!=null&&n.debug()){const d=Math.round((Date.now()-a)*100)/100,c=Math.round((Date.now()-l)*100)/100,g=c/16,p=(b,h)=>{for(b=String(b);b.length<h;)b=" "+b;return b};console.info(`%c⏱ ${p(c,5)} /${p(d,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*g,120))}deg 100% 31%);`,n==null?void 0:n.key)}return o}}function L(e,t,n,r){return{debug:()=>{var o;return(o=e==null?void 0:e.debugAll)!=null?o:e[t]},key:process.env.NODE_ENV==="development"&&n,onChange:r}}function Cd(e,t,n,r){const o=()=>{var a;return(a=s.getValue())!=null?a:e.options.renderFallbackValue},s={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(r),renderValue:o,getContext:V(()=>[e,n,t,s],(a,i,u,l)=>({table:a,column:i,row:u,cell:l,getValue:l.getValue,renderValue:l.renderValue}),L(e.options,"debugCells","cell.getContext"))};return e._features.forEach(a=>{a.createCell==null||a.createCell(s,n,t,e)},{}),s}function Sd(e,t,n,r){var o,s;const i={...e._getDefaultColumnDef(),...t},u=i.accessorKey;let l=(o=(s=i.id)!=null?s:u?typeof String.prototype.replaceAll=="function"?u.replaceAll(".","_"):u.replace(/\./g,"_"):void 0)!=null?o:typeof i.header=="string"?i.header:void 0,d;if(i.accessorFn?d=i.accessorFn:u&&(u.includes(".")?d=g=>{let p=g;for(const h of u.split(".")){var b;p=(b=p)==null?void 0:b[h],process.env.NODE_ENV!=="production"&&p===void 0&&console.warn(`"${h}" in deeply nested key "${u}" returned undefined.`)}return p}:d=g=>g[i.accessorKey]),!l)throw process.env.NODE_ENV!=="production"?new Error(i.accessorFn?"Columns require an id when using an accessorFn":"Columns require an id when using a non-string header"):new Error;let c={id:`${String(l)}`,accessorFn:d,parent:r,depth:n,columnDef:i,columns:[],getFlatColumns:V(()=>[!0],()=>{var g;return[c,...(g=c.columns)==null?void 0:g.flatMap(p=>p.getFlatColumns())]},L(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:V(()=>[e._getOrderColumnsFn()],g=>{var p;if((p=c.columns)!=null&&p.length){let b=c.columns.flatMap(h=>h.getLeafColumns());return g(b)}return[c]},L(e.options,"debugColumns","column.getLeafColumns"))};for(const g of e._features)g.createColumn==null||g.createColumn(c,e);return c}const ge="debugHeaders";function Po(e,t,n){var r;let s={id:(r=n.id)!=null?r:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const a=[],i=u=>{u.subHeaders&&u.subHeaders.length&&u.subHeaders.map(i),a.push(u)};return i(s),a},getContext:()=>({table:e,header:s,column:t})};return e._features.forEach(a=>{a.createHeader==null||a.createHeader(s,e)}),s}const _d={createTable:e=>{e.getHeaderGroups=V(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>{var s,a;const i=(s=r==null?void 0:r.map(c=>n.find(g=>g.id===c)).filter(Boolean))!=null?s:[],u=(a=o==null?void 0:o.map(c=>n.find(g=>g.id===c)).filter(Boolean))!=null?a:[],l=n.filter(c=>!(r!=null&&r.includes(c.id))&&!(o!=null&&o.includes(c.id)));return rn(t,[...i,...l,...u],e)},L(e.options,ge,"getHeaderGroups")),e.getCenterHeaderGroups=V(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r,o)=>(n=n.filter(s=>!(r!=null&&r.includes(s.id))&&!(o!=null&&o.includes(s.id))),rn(t,n,e,"center")),L(e.options,ge,"getCenterHeaderGroups")),e.getLeftHeaderGroups=V(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,r)=>{var o;const s=(o=r==null?void 0:r.map(a=>n.find(i=>i.id===a)).filter(Boolean))!=null?o:[];return rn(t,s,e,"left")},L(e.options,ge,"getLeftHeaderGroups")),e.getRightHeaderGroups=V(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,r)=>{var o;const s=(o=r==null?void 0:r.map(a=>n.find(i=>i.id===a)).filter(Boolean))!=null?o:[];return rn(t,s,e,"right")},L(e.options,ge,"getRightHeaderGroups")),e.getFooterGroups=V(()=>[e.getHeaderGroups()],t=>[...t].reverse(),L(e.options,ge,"getFooterGroups")),e.getLeftFooterGroups=V(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),L(e.options,ge,"getLeftFooterGroups")),e.getCenterFooterGroups=V(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),L(e.options,ge,"getCenterFooterGroups")),e.getRightFooterGroups=V(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),L(e.options,ge,"getRightFooterGroups")),e.getFlatHeaders=V(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,ge,"getFlatHeaders")),e.getLeftFlatHeaders=V(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,ge,"getLeftFlatHeaders")),e.getCenterFlatHeaders=V(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,ge,"getCenterFlatHeaders")),e.getRightFlatHeaders=V(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),L(e.options,ge,"getRightFlatHeaders")),e.getCenterLeafHeaders=V(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,ge,"getCenterLeafHeaders")),e.getLeftLeafHeaders=V(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,ge,"getLeftLeafHeaders")),e.getRightLeafHeaders=V(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var r;return!((r=n.subHeaders)!=null&&r.length)}),L(e.options,ge,"getRightLeafHeaders")),e.getLeafHeaders=V(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,r)=>{var o,s,a,i,u,l;return[...(o=(s=t[0])==null?void 0:s.headers)!=null?o:[],...(a=(i=n[0])==null?void 0:i.headers)!=null?a:[],...(u=(l=r[0])==null?void 0:l.headers)!=null?u:[]].map(d=>d.getLeafHeaders()).flat()},L(e.options,ge,"getLeafHeaders"))}};function rn(e,t,n,r){var o,s;let a=0;const i=function(g,p){p===void 0&&(p=1),a=Math.max(a,p),g.filter(b=>b.getIsVisible()).forEach(b=>{var h;(h=b.columns)!=null&&h.length&&i(b.columns,p+1)},0)};i(e);let u=[];const l=(g,p)=>{const b={depth:p,id:[r,`${p}`].filter(Boolean).join("_"),headers:[]},h=[];g.forEach(v=>{const x=[...h].reverse()[0],y=v.column.depth===b.depth;let w,C=!1;if(y&&v.column.parent?w=v.column.parent:(w=v.column,C=!0),x&&(x==null?void 0:x.column)===w)x.subHeaders.push(v);else{const _=Po(n,w,{id:[r,p,w.id,v==null?void 0:v.id].filter(Boolean).join("_"),isPlaceholder:C,placeholderId:C?`${h.filter(M=>M.column===w).length}`:void 0,depth:p,index:h.length});_.subHeaders.push(v),h.push(_)}b.headers.push(v),v.headerGroup=b}),u.push(b),p>0&&l(h,p-1)},d=t.map((g,p)=>Po(n,g,{depth:a,index:p}));l(d,a-1),u.reverse();const c=g=>g.filter(b=>b.column.getIsVisible()).map(b=>{let h=0,v=0,x=[0];b.subHeaders&&b.subHeaders.length?(x=[],c(b.subHeaders).forEach(w=>{let{colSpan:C,rowSpan:_}=w;h+=C,x.push(_)})):h=1;const y=Math.min(...x);return v=v+y,b.colSpan=h,b.rowSpan=v,{colSpan:h,rowSpan:v}});return c((o=(s=u[0])==null?void 0:s.headers)!=null?o:[]),u}const Rd=(e,t,n,r,o,s,a)=>{let i={id:t,index:r,original:n,depth:o,parentId:a,_valuesCache:{},_uniqueValuesCache:{},getValue:u=>{if(i._valuesCache.hasOwnProperty(u))return i._valuesCache[u];const l=e.getColumn(u);if(l!=null&&l.accessorFn)return i._valuesCache[u]=l.accessorFn(i.original,r),i._valuesCache[u]},getUniqueValues:u=>{if(i._uniqueValuesCache.hasOwnProperty(u))return i._uniqueValuesCache[u];const l=e.getColumn(u);if(l!=null&&l.accessorFn)return l.columnDef.getUniqueValues?(i._uniqueValuesCache[u]=l.columnDef.getUniqueValues(i.original,r),i._uniqueValuesCache[u]):(i._uniqueValuesCache[u]=[i.getValue(u)],i._uniqueValuesCache[u])},renderValue:u=>{var l;return(l=i.getValue(u))!=null?l:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>wd(i.subRows,u=>u.subRows),getParentRow:()=>i.parentId?e.getRow(i.parentId,!0):void 0,getParentRows:()=>{let u=[],l=i;for(;;){const d=l.getParentRow();if(!d)break;u.push(d),l=d}return u.reverse()},getAllCells:V(()=>[e.getAllLeafColumns()],u=>u.map(l=>Cd(e,i,l,l.id)),L(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:V(()=>[i.getAllCells()],u=>u.reduce((l,d)=>(l[d.column.id]=d,l),{}),L(e.options,"debugRows","getAllCellsByColumnId"))};for(let u=0;u<e._features.length;u++){const l=e._features[u];l==null||l.createRow==null||l.createRow(i,e)}return i},Ed={createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Ta=(e,t,n)=>{var r,o;const s=n==null||(r=n.toString())==null?void 0:r.toLowerCase();return!!(!((o=e.getValue(t))==null||(o=o.toString())==null||(o=o.toLowerCase())==null)&&o.includes(s))};Ta.autoRemove=e=>Me(e);const Va=(e,t,n)=>{var r;return!!(!((r=e.getValue(t))==null||(r=r.toString())==null)&&r.includes(n))};Va.autoRemove=e=>Me(e);const La=(e,t,n)=>{var r;return((r=e.getValue(t))==null||(r=r.toString())==null?void 0:r.toLowerCase())===(n==null?void 0:n.toLowerCase())};La.autoRemove=e=>Me(e);const Ba=(e,t,n)=>{var r;return(r=e.getValue(t))==null?void 0:r.includes(n)};Ba.autoRemove=e=>Me(e);const za=(e,t,n)=>!n.some(r=>{var o;return!((o=e.getValue(t))!=null&&o.includes(r))});za.autoRemove=e=>Me(e)||!(e!=null&&e.length);const Ha=(e,t,n)=>n.some(r=>{var o;return(o=e.getValue(t))==null?void 0:o.includes(r)});Ha.autoRemove=e=>Me(e)||!(e!=null&&e.length);const Ga=(e,t,n)=>e.getValue(t)===n;Ga.autoRemove=e=>Me(e);const Wa=(e,t,n)=>e.getValue(t)==n;Wa.autoRemove=e=>Me(e);const $r=(e,t,n)=>{let[r,o]=n;const s=e.getValue(t);return s>=r&&s<=o};$r.resolveFilterValue=e=>{let[t,n]=e,r=typeof t!="number"?parseFloat(t):t,o=typeof n!="number"?parseFloat(n):n,s=t===null||Number.isNaN(r)?-1/0:r,a=n===null||Number.isNaN(o)?1/0:o;if(s>a){const i=s;s=a,a=i}return[s,a]};$r.autoRemove=e=>Me(e)||Me(e[0])&&Me(e[1]);const ze={includesString:Ta,includesStringSensitive:Va,equalsString:La,arrIncludes:Ba,arrIncludesAll:za,arrIncludesSome:Ha,equals:Ga,weakEquals:Wa,inNumberRange:$r};function Me(e){return e==null||e===""}const Pd={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:Se("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=n==null?void 0:n.getValue(e.id);return typeof r=="string"?ze.includesString:typeof r=="number"?ze.inNumberRange:typeof r=="boolean"||r!==null&&typeof r=="object"?ze.equals:Array.isArray(r)?ze.arrIncludes:ze.weakEquals},e.getFilterFn=()=>{var n,r;return Rn(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(r=t.options.filterFns)==null?void 0:r[e.columnDef.filterFn])!=null?n:ze[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,r,o;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((r=t.options.enableColumnFilters)!=null?r:!0)&&((o=t.options.enableFilters)!=null?o:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return(n=t.getState().columnFilters)==null||(n=n.find(r=>r.id===e.id))==null?void 0:n.value},e.getFilterIndex=()=>{var n,r;return(n=(r=t.getState().columnFilters)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?n:-1},e.setFilterValue=n=>{t.setColumnFilters(r=>{const o=e.getFilterFn(),s=r==null?void 0:r.find(d=>d.id===e.id),a=Je(n,s?s.value:void 0);if(Mo(o,a,e)){var i;return(i=r==null?void 0:r.filter(d=>d.id!==e.id))!=null?i:[]}const u={id:e.id,value:a};if(s){var l;return(l=r==null?void 0:r.map(d=>d.id===e.id?u:d))!=null?l:[]}return r!=null&&r.length?[...r,u]:[u]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{const n=e.getAllLeafColumns(),r=o=>{var s;return(s=Je(t,o))==null?void 0:s.filter(a=>{const i=n.find(u=>u.id===a.id);if(i){const u=i.getFilterFn();if(Mo(u,a.value,i))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(r)},e.resetColumnFilters=t=>{var n,r;e.setColumnFilters(t?[]:(n=(r=e.initialState)==null?void 0:r.columnFilters)!=null?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function Mo(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const Md=(e,t,n)=>n.reduce((r,o)=>{const s=o.getValue(e);return r+(typeof s=="number"?s:0)},0),Nd=(e,t,n)=>{let r;return n.forEach(o=>{const s=o.getValue(e);s!=null&&(r>s||r===void 0&&s>=s)&&(r=s)}),r},Ad=(e,t,n)=>{let r;return n.forEach(o=>{const s=o.getValue(e);s!=null&&(r<s||r===void 0&&s>=s)&&(r=s)}),r},jd=(e,t,n)=>{let r,o;return n.forEach(s=>{const a=s.getValue(e);a!=null&&(r===void 0?a>=a&&(r=o=a):(r>a&&(r=a),o<a&&(o=a)))}),[r,o]},kd=(e,t)=>{let n=0,r=0;if(t.forEach(o=>{let s=o.getValue(e);s!=null&&(s=+s)>=s&&(++n,r+=s)}),n)return r/n},Id=(e,t)=>{if(!t.length)return;const n=t.map(s=>s.getValue(e));if(!yd(n))return;if(n.length===1)return n[0];const r=Math.floor(n.length/2),o=n.sort((s,a)=>s-a);return n.length%2!==0?o[r]:(o[r-1]+o[r])/2},Fd=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),$d=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,Od=(e,t)=>t.length,Vn={sum:Md,min:Nd,max:Ad,extent:jd,mean:kd,median:Id,unique:Fd,uniqueCount:$d,count:Od},Dd={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Se("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(r=>r!==e.id):[...n??[],e.id])},e.getCanGroup=()=>{var n,r;return((n=e.columnDef.enableGrouping)!=null?n:!0)&&((r=t.options.enableGrouping)!=null?r:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const n=t.getCoreRowModel().flatRows[0],r=n==null?void 0:n.getValue(e.id);if(typeof r=="number")return Vn.sum;if(Object.prototype.toString.call(r)==="[object Date]")return Vn.extent},e.getAggregationFn=()=>{var n,r;if(!e)throw new Error;return Rn(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(r=t.options.aggregationFns)==null?void 0:r[e.columnDef.aggregationFn])!=null?n:Vn[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,r;e.setGrouping(t?[]:(n=(r=e.initialState)==null?void 0:r.grouping)!=null?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const r=t.getColumn(n);return r!=null&&r.columnDef.getGroupingValue?(e._groupingValuesCache[n]=r.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,r)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var o;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((o=n.subRows)!=null&&o.length)}}};function Td(e,t,n){if(!(t!=null&&t.length)||!n)return e;const r=e.filter(s=>!t.includes(s.id));return n==="remove"?r:[...t.map(s=>e.find(a=>a.id===s)).filter(Boolean),...r]}const Vd={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Se("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=V(n=>[Ot(t,n)],n=>n.findIndex(r=>r.id===e.id),L(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var r;return((r=Ot(t,n)[0])==null?void 0:r.id)===e.id},e.getIsLastColumn=n=>{var r;const o=Ot(t,n);return((r=o[o.length-1])==null?void 0:r.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},e._getOrderColumnsFn=V(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,r)=>o=>{let s=[];if(!(t!=null&&t.length))s=o;else{const a=[...t],i=[...o];for(;i.length&&a.length;){const u=a.shift(),l=i.findIndex(d=>d.id===u);l>-1&&s.push(i.splice(l,1)[0])}s=[...s,...i]}return Td(s,n,r)},L(e.options,"debugTable","_getOrderColumnsFn"))}},Ln=()=>({left:[],right:[]}),Ld={getInitialState:e=>({columnPinning:Ln(),...e}),getDefaultOptions:e=>({onColumnPinningChange:Se("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{const r=e.getLeafColumns().map(o=>o.id).filter(Boolean);t.setColumnPinning(o=>{var s,a;if(n==="right"){var i,u;return{left:((i=o==null?void 0:o.left)!=null?i:[]).filter(c=>!(r!=null&&r.includes(c))),right:[...((u=o==null?void 0:o.right)!=null?u:[]).filter(c=>!(r!=null&&r.includes(c))),...r]}}if(n==="left"){var l,d;return{left:[...((l=o==null?void 0:o.left)!=null?l:[]).filter(c=>!(r!=null&&r.includes(c))),...r],right:((d=o==null?void 0:o.right)!=null?d:[]).filter(c=>!(r!=null&&r.includes(c)))}}return{left:((s=o==null?void 0:o.left)!=null?s:[]).filter(c=>!(r!=null&&r.includes(c))),right:((a=o==null?void 0:o.right)!=null?a:[]).filter(c=>!(r!=null&&r.includes(c)))}})},e.getCanPin=()=>e.getLeafColumns().some(r=>{var o,s,a;return((o=r.columnDef.enablePinning)!=null?o:!0)&&((s=(a=t.options.enableColumnPinning)!=null?a:t.options.enablePinning)!=null?s:!0)}),e.getIsPinned=()=>{const n=e.getLeafColumns().map(i=>i.id),{left:r,right:o}=t.getState().columnPinning,s=n.some(i=>r==null?void 0:r.includes(i)),a=n.some(i=>o==null?void 0:o.includes(i));return s?"left":a?"right":!1},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();return o?(n=(r=t.getState().columnPinning)==null||(r=r[o])==null?void 0:r.indexOf(e.id))!=null?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=V(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,r,o)=>{const s=[...r??[],...o??[]];return n.filter(a=>!s.includes(a.column.id))},L(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=V(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(n,r)=>(r??[]).map(s=>n.find(a=>a.column.id===s)).filter(Boolean).map(s=>({...s,position:"left"})),L(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=V(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,r)=>(r??[]).map(s=>n.find(a=>a.column.id===s)).filter(Boolean).map(s=>({...s,position:"right"})),L(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,r;return e.setColumnPinning(t?Ln():(n=(r=e.initialState)==null?void 0:r.columnPinning)!=null?n:Ln())},e.getIsSomeColumnsPinned=t=>{var n;const r=e.getState().columnPinning;if(!t){var o,s;return!!((o=r.left)!=null&&o.length||(s=r.right)!=null&&s.length)}return!!((n=r[t])!=null&&n.length)},e.getLeftLeafColumns=V(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(r=>t.find(o=>o.id===r)).filter(Boolean),L(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=V(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(r=>t.find(o=>o.id===r)).filter(Boolean),L(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=V(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,r)=>{const o=[...n??[],...r??[]];return t.filter(s=>!o.includes(s.id))},L(e.options,"debugColumns","getCenterLeafColumns"))}};function Bd(e){return e||(typeof document<"u"?document:null)}const on={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},Bn=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),zd={getDefaultColumnDef:()=>on,getInitialState:e=>({columnSizing:{},columnSizingInfo:Bn(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:Se("columnSizing",e),onColumnSizingInfoChange:Se("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,r,o;const s=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:on.minSize,(r=s??e.columnDef.size)!=null?r:on.size),(o=e.columnDef.maxSize)!=null?o:on.maxSize)},e.getStart=V(n=>[n,Ot(t,n),t.getState().columnSizing],(n,r)=>r.slice(0,e.getIndex(n)).reduce((o,s)=>o+s.getSize(),0),L(t.options,"debugColumns","getStart")),e.getAfter=V(n=>[n,Ot(t,n),t.getState().columnSizing],(n,r)=>r.slice(e.getIndex(n)+1).reduce((o,s)=>o+s.getSize(),0),L(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(n=>{let{[e.id]:r,...o}=n;return o})},e.getCanResize=()=>{var n,r;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((r=t.options.enableColumnResizing)!=null?r:!0)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let n=0;const r=o=>{if(o.subHeaders.length)o.subHeaders.forEach(r);else{var s;n+=(s=o.column.getSize())!=null?s:0}};return r(e),n},e.getStart=()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},e.getResizeHandler=n=>{const r=t.getColumn(e.column.id),o=r==null?void 0:r.getCanResize();return s=>{if(!r||!o||(s.persist==null||s.persist(),zn(s)&&s.touches&&s.touches.length>1))return;const a=e.getSize(),i=e?e.getLeafHeaders().map(x=>[x.column.id,x.column.getSize()]):[[r.id,r.getSize()]],u=zn(s)?Math.round(s.touches[0].clientX):s.clientX,l={},d=(x,y)=>{typeof y=="number"&&(t.setColumnSizingInfo(w=>{var C,_;const M=t.options.columnResizeDirection==="rtl"?-1:1,E=(y-((C=w==null?void 0:w.startOffset)!=null?C:0))*M,R=Math.max(E/((_=w==null?void 0:w.startSize)!=null?_:0),-.999999);return w.columnSizingStart.forEach(O=>{let[T,D]=O;l[T]=Math.round(Math.max(D+D*R,0)*100)/100}),{...w,deltaOffset:E,deltaPercentage:R}}),(t.options.columnResizeMode==="onChange"||x==="end")&&t.setColumnSizing(w=>({...w,...l})))},c=x=>d("move",x),g=x=>{d("end",x),t.setColumnSizingInfo(y=>({...y,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},p=Bd(n),b={moveHandler:x=>c(x.clientX),upHandler:x=>{p==null||p.removeEventListener("mousemove",b.moveHandler),p==null||p.removeEventListener("mouseup",b.upHandler),g(x.clientX)}},h={moveHandler:x=>(x.cancelable&&(x.preventDefault(),x.stopPropagation()),c(x.touches[0].clientX),!1),upHandler:x=>{var y;p==null||p.removeEventListener("touchmove",h.moveHandler),p==null||p.removeEventListener("touchend",h.upHandler),x.cancelable&&(x.preventDefault(),x.stopPropagation()),g((y=x.touches[0])==null?void 0:y.clientX)}},v=Hd()?{passive:!1}:!1;zn(s)?(p==null||p.addEventListener("touchmove",h.moveHandler,v),p==null||p.addEventListener("touchend",h.upHandler,v)):(p==null||p.addEventListener("mousemove",b.moveHandler,v),p==null||p.addEventListener("mouseup",b.upHandler,v)),t.setColumnSizingInfo(x=>({...x,startOffset:u,startSize:a,deltaOffset:0,deltaPercentage:0,columnSizingStart:i,isResizingColumn:r.id}))}}},createTable:e=>{e.setColumnSizing=t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?Bn():(n=e.initialState.columnSizingInfo)!=null?n:Bn())},e.getTotalSize=()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getLeftTotalSize=()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getCenterTotalSize=()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0},e.getRightTotalSize=()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((r,o)=>r+o.getSize(),0))!=null?t:0}}};let sn=null;function Hd(){if(typeof sn=="boolean")return sn;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return sn=e,sn}function zn(e){return e.type==="touchstart"}const Gd={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Se("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(r=>({...r,[e.id]:n??!e.getIsVisible()}))},e.getIsVisible=()=>{var n,r;const o=e.columns;return(n=o.length?o.some(s=>s.getIsVisible()):(r=t.getState().columnVisibility)==null?void 0:r[e.id])!=null?n:!0},e.getCanHide=()=>{var n,r;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((r=t.options.enableHiding)!=null?r:!0)},e.getToggleVisibilityHandler=()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=V(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(r=>r.column.getIsVisible()),L(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=V(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,r,o)=>[...n,...r,...o],L(t.options,"debugRows","getVisibleCells"))},createTable:e=>{const t=(n,r)=>V(()=>[r(),r().filter(o=>o.getIsVisible()).map(o=>o.id).join("_")],o=>o.filter(s=>s.getIsVisible==null?void 0:s.getIsVisible()),L(e.options,"debugColumns",n));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),e.resetColumnVisibility=n=>{var r;e.setColumnVisibility(n?{}:(r=e.initialState.columnVisibility)!=null?r:{})},e.toggleAllColumnsVisible=n=>{var r;n=(r=n)!=null?r:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((o,s)=>({...o,[s.id]:n||!(s.getCanHide!=null&&s.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>n=>{var r;e.toggleAllColumnsVisible((r=n.target)==null?void 0:r.checked)}}};function Ot(e,t){return t?t==="center"?e.getCenterVisibleLeafColumns():t==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const Wd={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},Ud={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:Se("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;const r=(n=e.getCoreRowModel().flatRows[0])==null||(n=n._getAllCellsByColumnId()[t.id])==null?void 0:n.getValue();return typeof r=="string"||typeof r=="number"}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,r,o,s;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((r=t.options.enableGlobalFilter)!=null?r:!0)&&((o=t.options.enableFilters)!=null?o:!0)&&((s=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?s:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>ze.includesString,e.getGlobalFilterFn=()=>{var t,n;const{globalFilterFn:r}=e.options;return Rn(r)?r:r==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[r])!=null?t:ze[r]},e.setGlobalFilter=t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},qd={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Se("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var r,o;if(!t){e._queue(()=>{t=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetExpanded)!=null?r:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=r=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(r),e.toggleAllRowsExpanded=r=>{r??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=r=>{var o,s;e.setExpanded(r?{}:(o=(s=e.initialState)==null?void 0:s.expanded)!=null?o:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(r=>r.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>r=>{r.persist==null||r.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const r=e.getState().expanded;return r===!0||Object.values(r).some(Boolean)},e.getIsAllRowsExpanded=()=>{const r=e.getState().expanded;return typeof r=="boolean"?r===!0:!(!Object.keys(r).length||e.getRowModel().flatRows.some(o=>!o.getIsExpanded()))},e.getExpandedDepth=()=>{let r=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(s=>{const a=s.split(".");r=Math.max(r,a.length)}),r},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(r=>{var o;const s=r===!0?!0:!!(r!=null&&r[e.id]);let a={};if(r===!0?Object.keys(t.getRowModel().rowsById).forEach(i=>{a[i]=!0}):a=r,n=(o=n)!=null?o:!s,!s&&n)return{...a,[e.id]:!0};if(s&&!n){const{[e.id]:i,...u}=a;return u}return r})},e.getIsExpanded=()=>{var n;const r=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:r===!0||r!=null&&r[e.id])},e.getCanExpand=()=>{var n,r,o;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((r=t.options.enableExpanding)!=null?r:!0)&&!!((o=e.subRows)!=null&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,r=e;for(;n&&r.parentId;)r=t.getRow(r.parentId,!0),n=r.getIsExpanded();return n},e.getToggleExpandedHandler=()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}}},Qn=0,Jn=10,Hn=()=>({pageIndex:Qn,pageSize:Jn}),Xd={getInitialState:e=>({...e,pagination:{...Hn(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:Se("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var r,o;if(!t){e._queue(()=>{t=!0});return}if((r=(o=e.options.autoResetAll)!=null?o:e.options.autoResetPageIndex)!=null?r:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=r=>{const o=s=>Je(r,s);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(o)},e.resetPagination=r=>{var o;e.setPagination(r?Hn():(o=e.initialState.pagination)!=null?o:Hn())},e.setPageIndex=r=>{e.setPagination(o=>{let s=Je(r,o.pageIndex);const a=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return s=Math.max(0,Math.min(s,a)),{...o,pageIndex:s}})},e.resetPageIndex=r=>{var o,s;e.setPageIndex(r?Qn:(o=(s=e.initialState)==null||(s=s.pagination)==null?void 0:s.pageIndex)!=null?o:Qn)},e.resetPageSize=r=>{var o,s;e.setPageSize(r?Jn:(o=(s=e.initialState)==null||(s=s.pagination)==null?void 0:s.pageSize)!=null?o:Jn)},e.setPageSize=r=>{e.setPagination(o=>{const s=Math.max(1,Je(r,o.pageSize)),a=o.pageSize*o.pageIndex,i=Math.floor(a/s);return{...o,pageIndex:i,pageSize:s}})},e.setPageCount=r=>e.setPagination(o=>{var s;let a=Je(r,(s=e.options.pageCount)!=null?s:-1);return typeof a=="number"&&(a=Math.max(-1,a)),{...o,pageCount:a}}),e.getPageOptions=V(()=>[e.getPageCount()],r=>{let o=[];return r&&r>0&&(o=[...new Array(r)].fill(null).map((s,a)=>a)),o},L(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:r}=e.getState().pagination,o=e.getPageCount();return o===-1?!0:o===0?!1:r<o-1},e.previousPage=()=>e.setPageIndex(r=>r-1),e.nextPage=()=>e.setPageIndex(r=>r+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var r;return(r=e.options.pageCount)!=null?r:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var r;return(r=e.options.rowCount)!=null?r:e.getPrePaginationRowModel().rows.length}}},Gn=()=>({top:[],bottom:[]}),Yd={getInitialState:e=>({rowPinning:Gn(),...e}),getDefaultOptions:e=>({onRowPinningChange:Se("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,r,o)=>{const s=r?e.getLeafRows().map(u=>{let{id:l}=u;return l}):[],a=o?e.getParentRows().map(u=>{let{id:l}=u;return l}):[],i=new Set([...a,e.id,...s]);t.setRowPinning(u=>{var l,d;if(n==="bottom"){var c,g;return{top:((c=u==null?void 0:u.top)!=null?c:[]).filter(h=>!(i!=null&&i.has(h))),bottom:[...((g=u==null?void 0:u.bottom)!=null?g:[]).filter(h=>!(i!=null&&i.has(h))),...Array.from(i)]}}if(n==="top"){var p,b;return{top:[...((p=u==null?void 0:u.top)!=null?p:[]).filter(h=>!(i!=null&&i.has(h))),...Array.from(i)],bottom:((b=u==null?void 0:u.bottom)!=null?b:[]).filter(h=>!(i!=null&&i.has(h)))}}return{top:((l=u==null?void 0:u.top)!=null?l:[]).filter(h=>!(i!=null&&i.has(h))),bottom:((d=u==null?void 0:u.bottom)!=null?d:[]).filter(h=>!(i!=null&&i.has(h)))}})},e.getCanPin=()=>{var n;const{enableRowPinning:r,enablePinning:o}=t.options;return typeof r=="function"?r(e):(n=r??o)!=null?n:!0},e.getIsPinned=()=>{const n=[e.id],{top:r,bottom:o}=t.getState().rowPinning,s=n.some(i=>r==null?void 0:r.includes(i)),a=n.some(i=>o==null?void 0:o.includes(i));return s?"top":a?"bottom":!1},e.getPinnedIndex=()=>{var n,r;const o=e.getIsPinned();if(!o)return-1;const s=(n=o==="top"?t.getTopRows():t.getBottomRows())==null?void 0:n.map(a=>{let{id:i}=a;return i});return(r=s==null?void 0:s.indexOf(e.id))!=null?r:-1}},createTable:e=>{e.setRowPinning=t=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,r;return e.setRowPinning(t?Gn():(n=(r=e.initialState)==null?void 0:r.rowPinning)!=null?n:Gn())},e.getIsSomeRowsPinned=t=>{var n;const r=e.getState().rowPinning;if(!t){var o,s;return!!((o=r.top)!=null&&o.length||(s=r.bottom)!=null&&s.length)}return!!((n=r[t])!=null&&n.length)},e._getPinnedRows=(t,n,r)=>{var o;return((o=e.options.keepPinnedRows)==null||o?(n??[]).map(a=>{const i=e.getRow(a,!0);return i.getIsAllParentsExpanded()?i:null}):(n??[]).map(a=>t.find(i=>i.id===a))).filter(Boolean).map(a=>({...a,position:r}))},e.getTopRows=V(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),L(e.options,"debugRows","getTopRows")),e.getBottomRows=V(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),L(e.options,"debugRows","getBottomRows")),e.getCenterRows=V(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(t,n,r)=>{const o=new Set([...n??[],...r??[]]);return t.filter(s=>!o.has(s.id))},L(e.options,"debugRows","getCenterRows"))}},Zd={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Se("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const r={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(s=>{s.getCanSelect()&&(r[s.id]=!0)}):o.forEach(s=>{delete r[s.id]}),r})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{const r=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(s=>{er(o,s.id,r,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=V(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?Wn(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=V(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?Wn(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=V(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?Wn(e,n):{rows:[],flatRows:[],rowsById:{}},L(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let r=!!(t.length&&Object.keys(n).length);return r&&t.some(o=>o.getCanSelect()&&!n[o.id])&&(r=!1),r},e.getIsAllPageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows.filter(o=>o.getCanSelect()),{rowSelection:n}=e.getState();let r=!!t.length;return r&&t.some(o=>!n[o.id])&&(r=!1),r},e.getIsSomeRowsSelected=()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.filter(n=>n.getCanSelect()).some(n=>n.getIsSelected()||n.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,r)=>{const o=e.getIsSelected();t.setRowSelection(s=>{var a;if(n=typeof n<"u"?n:!o,e.getCanSelect()&&o===n)return s;const i={...s};return er(i,e.id,n,(a=r==null?void 0:r.selectChildren)!=null?a:!0,t),i})},e.getIsSelected=()=>{const{rowSelection:n}=t.getState();return Or(e,n)},e.getIsSomeSelected=()=>{const{rowSelection:n}=t.getState();return tr(e,n)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:n}=t.getState();return tr(e,n)==="all"},e.getCanSelect=()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},e.getCanSelectSubRows=()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},e.getCanMultiSelect=()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},e.getToggleSelectedHandler=()=>{const n=e.getCanSelect();return r=>{var o;n&&e.toggleSelected((o=r.target)==null?void 0:o.checked)}}}},er=(e,t,n,r,o)=>{var s;const a=o.getRow(t,!0);n?(a.getCanMultiSelect()||Object.keys(e).forEach(i=>delete e[i]),a.getCanSelect()&&(e[t]=!0)):delete e[t],r&&(s=a.subRows)!=null&&s.length&&a.getCanSelectSubRows()&&a.subRows.forEach(i=>er(e,i.id,n,r,o))};function Wn(e,t){const n=e.getState().rowSelection,r=[],o={},s=function(a,i){return a.map(u=>{var l;const d=Or(u,n);if(d&&(r.push(u),o[u.id]=u),(l=u.subRows)!=null&&l.length&&(u={...u,subRows:s(u.subRows)}),d)return u}).filter(Boolean)};return{rows:s(t.rows),flatRows:r,rowsById:o}}function Or(e,t){var n;return(n=t[e.id])!=null?n:!1}function tr(e,t,n){var r;if(!((r=e.subRows)!=null&&r.length))return!1;let o=!0,s=!1;return e.subRows.forEach(a=>{if(!(s&&!o)&&(a.getCanSelect()&&(Or(a,t)?s=!0:o=!1),a.subRows&&a.subRows.length)){const i=tr(a,t);i==="all"?s=!0:(i==="some"&&(s=!0),o=!1)}}),o?"all":s?"some":!1}const nr=/([0-9]+)/gm,Kd=(e,t,n)=>Ua(rt(e.getValue(n)).toLowerCase(),rt(t.getValue(n)).toLowerCase()),Qd=(e,t,n)=>Ua(rt(e.getValue(n)),rt(t.getValue(n))),Jd=(e,t,n)=>Dr(rt(e.getValue(n)).toLowerCase(),rt(t.getValue(n)).toLowerCase()),ef=(e,t,n)=>Dr(rt(e.getValue(n)),rt(t.getValue(n))),tf=(e,t,n)=>{const r=e.getValue(n),o=t.getValue(n);return r>o?1:r<o?-1:0},nf=(e,t,n)=>Dr(e.getValue(n),t.getValue(n));function Dr(e,t){return e===t?0:e>t?1:-1}function rt(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Ua(e,t){const n=e.split(nr).filter(Boolean),r=t.split(nr).filter(Boolean);for(;n.length&&r.length;){const o=n.shift(),s=r.shift(),a=parseInt(o,10),i=parseInt(s,10),u=[a,i].sort();if(isNaN(u[0])){if(o>s)return 1;if(s>o)return-1;continue}if(isNaN(u[1]))return isNaN(a)?-1:1;if(a>i)return 1;if(i>a)return-1}return n.length-r.length}const $t={alphanumeric:Kd,alphanumericCaseSensitive:Qd,text:Jd,textCaseSensitive:ef,datetime:tf,basic:nf},rf={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:Se("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let r=!1;for(const o of n){const s=o==null?void 0:o.getValue(e.id);if(Object.prototype.toString.call(s)==="[object Date]")return $t.datetime;if(typeof s=="string"&&(r=!0,s.split(nr).length>1))return $t.alphanumeric}return r?$t.text:$t.basic},e.getAutoSortDir=()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var n,r;if(!e)throw new Error;return Rn(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(r=t.options.sortingFns)==null?void 0:r[e.columnDef.sortingFn])!=null?n:$t[e.columnDef.sortingFn]},e.toggleSorting=(n,r)=>{const o=e.getNextSortingOrder(),s=typeof n<"u"&&n!==null;t.setSorting(a=>{const i=a==null?void 0:a.find(p=>p.id===e.id),u=a==null?void 0:a.findIndex(p=>p.id===e.id);let l=[],d,c=s?n:o==="desc";if(a!=null&&a.length&&e.getCanMultiSort()&&r?i?d="toggle":d="add":a!=null&&a.length&&u!==a.length-1?d="replace":i?d="toggle":d="replace",d==="toggle"&&(s||o||(d="remove")),d==="add"){var g;l=[...a,{id:e.id,desc:c}],l.splice(0,l.length-((g=t.options.maxMultiSortColCount)!=null?g:Number.MAX_SAFE_INTEGER))}else d==="toggle"?l=a.map(p=>p.id===e.id?{...p,desc:c}:p):d==="remove"?l=a.filter(p=>p.id!==e.id):l=[{id:e.id,desc:c}];return l})},e.getFirstSortDir=()=>{var n,r;return((n=(r=e.columnDef.sortDescFirst)!=null?r:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=n=>{var r,o;const s=e.getFirstSortDir(),a=e.getIsSorted();return a?a!==s&&((r=t.options.enableSortingRemoval)==null||r)&&(!(n&&(o=t.options.enableMultiRemove)!=null)||o)?!1:a==="desc"?"asc":"desc":s},e.getCanSort=()=>{var n,r;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((r=t.options.enableSorting)!=null?r:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,r;return(n=(r=e.columnDef.enableMultiSort)!=null?r:t.options.enableMultiSort)!=null?n:!!e.accessorFn},e.getIsSorted=()=>{var n;const r=(n=t.getState().sorting)==null?void 0:n.find(o=>o.id===e.id);return r?r.desc?"desc":"asc":!1},e.getSortIndex=()=>{var n,r;return(n=(r=t.getState().sorting)==null?void 0:r.findIndex(o=>o.id===e.id))!=null?n:-1},e.clearSorting=()=>{t.setSorting(n=>n!=null&&n.length?n.filter(r=>r.id!==e.id):[])},e.getToggleSortingHandler=()=>{const n=e.getCanSort();return r=>{n&&(r.persist==null||r.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(r):!1))}}},createTable:e=>{e.setSorting=t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,r;e.setSorting(t?[]:(n=(r=e.initialState)==null?void 0:r.sorting)!=null?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},of=[_d,Gd,Vd,Ld,Ed,Pd,Wd,Ud,rf,Dd,qd,Xd,Yd,Zd,zd];function sf(e){var t,n;process.env.NODE_ENV!=="production"&&(e.debugAll||e.debugTable)&&console.info("Creating Table Instance...");const r=[...of,...(t=e._features)!=null?t:[]];let o={_features:r};const s=o._features.reduce((g,p)=>Object.assign(g,p.getDefaultOptions==null?void 0:p.getDefaultOptions(o)),{}),a=g=>o.options.mergeOptions?o.options.mergeOptions(s,g):{...s,...g};let u={...{},...(n=e.initialState)!=null?n:{}};o._features.forEach(g=>{var p;u=(p=g.getInitialState==null?void 0:g.getInitialState(u))!=null?p:u});const l=[];let d=!1;const c={_features:r,options:{...s,...e},initialState:u,_queue:g=>{l.push(g),d||(d=!0,Promise.resolve().then(()=>{for(;l.length;)l.shift()();d=!1}).catch(p=>setTimeout(()=>{throw p})))},reset:()=>{o.setState(o.initialState)},setOptions:g=>{const p=Je(g,o.options);o.options=a(p)},getState:()=>o.options.state,setState:g=>{o.options.onStateChange==null||o.options.onStateChange(g)},_getRowId:(g,p,b)=>{var h;return(h=o.options.getRowId==null?void 0:o.options.getRowId(g,p,b))!=null?h:`${b?[b.id,p].join("."):p}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(g,p)=>{let b=(p?o.getPrePaginationRowModel():o.getRowModel()).rowsById[g];if(!b&&(b=o.getCoreRowModel().rowsById[g],!b))throw process.env.NODE_ENV!=="production"?new Error(`getRow could not find row with ID: ${g}`):new Error;return b},_getDefaultColumnDef:V(()=>[o.options.defaultColumn],g=>{var p;return g=(p=g)!=null?p:{},{header:b=>{const h=b.header.column.columnDef;return h.accessorKey?h.accessorKey:h.accessorFn?h.id:null},cell:b=>{var h,v;return(h=(v=b.renderValue())==null||v.toString==null?void 0:v.toString())!=null?h:null},...o._features.reduce((b,h)=>Object.assign(b,h.getDefaultColumnDef==null?void 0:h.getDefaultColumnDef()),{}),...g}},L(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>o.options.columns,getAllColumns:V(()=>[o._getColumnDefs()],g=>{const p=function(b,h,v){return v===void 0&&(v=0),b.map(x=>{const y=Sd(o,x,v,h),w=x;return y.columns=w.columns?p(w.columns,y,v+1):[],y})};return p(g)},L(e,"debugColumns","getAllColumns")),getAllFlatColumns:V(()=>[o.getAllColumns()],g=>g.flatMap(p=>p.getFlatColumns()),L(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:V(()=>[o.getAllFlatColumns()],g=>g.reduce((p,b)=>(p[b.id]=b,p),{}),L(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:V(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(g,p)=>{let b=g.flatMap(h=>h.getLeafColumns());return p(b)},L(e,"debugColumns","getAllLeafColumns")),getColumn:g=>{const p=o._getAllFlatColumnsById()[g];return process.env.NODE_ENV!=="production"&&!p&&console.error(`[Table] Column with id '${g}' does not exist.`),p}};Object.assign(o,c);for(let g=0;g<o._features.length;g++){const p=o._features[g];p==null||p.createTable==null||p.createTable(o)}return o}function af(){return e=>V(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},r=function(o,s,a){s===void 0&&(s=0);const i=[];for(let l=0;l<o.length;l++){const d=Rd(e,e._getRowId(o[l],l,a),o[l],l,s,void 0,a==null?void 0:a.id);if(n.flatRows.push(d),n.rowsById[d.id]=d,i.push(d),e.options.getSubRows){var u;d.originalSubRows=e.options.getSubRows(o[l],l),(u=d.originalSubRows)!=null&&u.length&&(d.subRows=r(d.originalSubRows,s+1,d))}}return i};return n.rows=r(t),n},L(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function No(e,t){return e?lf(e)?m.createElement(e,t):e:null}function lf(e){return cf(e)||typeof e=="function"||uf(e)}function cf(e){return typeof e=="function"&&(()=>{const t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()}function uf(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function df(e){const t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=m.useState(()=>({current:sf(t)})),[r,o]=m.useState(()=>n.current.initialState);return n.current.setOptions(s=>({...s,...e,state:{...r,...e.state},onStateChange:a=>{o(a),e.onStateChange==null||e.onStateChange(a)}})),n.current}const Dt=m.forwardRef(({checked:e,onChange:t,disabled:n,"aria-label":r,className:o,...s},a)=>{const i=m.useRef(null);return m.useImperativeHandle(a,()=>i.current),m.useEffect(()=>{i.current&&(i.current.indeterminate=e==="indeterminate")},[e]),f.jsx("input",{ref:i,type:"checkbox",checked:e===!0,onChange:u=>t==null?void 0:t(u.target.checked),disabled:n,"aria-label":r,className:F("h-4 w-4 rounded border border-gray-300 focus:ring-2 focus:ring-black focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-50","accent-black [&:checked]:bg-black [&:checked]:border-black","checked:bg-black checked:border-black",o),...s})});Dt.displayName="Checkbox";const qa=Q("w-full border-collapse bg-white overflow-hidden relative before:content-[''] before:absolute before:inset-0 before:z-[101] before:pointer-events-none before:border before:border-[#e3e3e3] before:mix-blend-luminosity before:shadow-[var(--p-shadow-bevel-100)]",{variants:{condensed:{true:"text-sm",false:""},hasZebraStriping:{true:"[&_tbody_tr:nth-child(even)]:bg-gray-50",false:""},loading:{true:"opacity-50 pointer-events-none",false:""},hasIndexFilters:{true:"before:rounded-b-[8px]",false:"before:rounded-[8px]"}},defaultVariants:{condensed:!1,hasZebraStriping:!1,loading:!1,hasIndexFilters:!1}}),Ke=Q("border-b border-gray-100 overflow-hidden bg-[#f7fafc] p-2 text-left text-xs font-medium text-gray-500 capitalize",{variants:{alignment:{start:"text-left",center:"text-center",end:"text-right"},sortable:{true:"cursor-pointer hover:bg-gray-100",false:""},sticky:{true:"sticky right-0 bg-gray-50",false:""},isSelectionColumn:{true:"w-12",false:""}},defaultVariants:{alignment:"start",sortable:!1,sticky:!1,isSelectionColumn:!1}}),Xa=Q("p-2 whitespace-nowrap text-sm text-gray-900",{variants:{alignment:{start:"text-left",center:"text-center",end:"text-right"},sticky:{true:"sticky right-0 bg-white",false:""},isSelectionColumn:{true:"w-12",false:""}},defaultVariants:{alignment:"start",sticky:!1,isSelectionColumn:!1}}),Ya=m.forwardRef(({headings:e,promotedBulkActions:t,bulkActions:n,children:r,emptyState:o,sort:s,paginatedSelectAllActionText:a,paginatedSelectAllText:i,lastColumnSticky:u=!1,selectable:l=!0,sortable:d,defaultSortDirection:c="descending",sortDirection:g,sortColumnIndex:p,onSort:b,sortToggleLabels:h,hasZebraStriping:v=!1,pagination:x,itemCount:y,selectedItemsCount:w,resourceName:C,loading:_=!1,hasMoreItems:M,condensed:E=!1,onSelectionChange:R,className:O,asChild:T=!1,data:D,columns:A,hasIndexFilters:G=!1,...z},q)=>{const N=m.useMemo(()=>D&&A?df({data:D,columns:A,getCoreRowModel:af()}):null,[D,A]),B=()=>{const $=typeof w=="number"&&w>0||w==="All",Z=()=>!n||n.length===0||!$?null:f.jsx("div",{className:"flex items-center gap-2 ml-auto",children:n.map((P,k)=>"actions"in P?P.actions.map((I,X)=>f.jsx(oe,{variant:"primary",size:"slim",onClick:I.onAction,disabled:I.disabled,tone:I.destructive?"critical":void 0,children:I.content},`${k}-${X}`)):f.jsx(oe,{variant:"primary",size:"slim",onClick:P.onAction,disabled:P.disabled,tone:P.destructive?"critical":void 0,children:P.content},k))});return N?f.jsx("thead",{children:N.getHeaderGroups().map(P=>f.jsxs("tr",{children:[l&&f.jsx("th",{className:F(Ke({isSelectionColumn:!0})),children:f.jsx(Dt,{checked:w==="All"||w===y&&y>0?!0:typeof w=="number"&&w>0?"indeterminate":!1,onChange:k=>{R&&R("page",k,void 0,void 0)},"aria-label":`Select all ${(C==null?void 0:C.plural)||"items"}`})}),$?f.jsx("th",{colSpan:P.headers.length,className:F(Ke({alignment:"start"}),"text-left"),children:f.jsxs("div",{className:"flex items-center justify-between w-full",children:[f.jsx(ye,{variant:"bodyMd",as:"span",children:w==="All"?`All ${(C==null?void 0:C.plural)||"items"} selected`:`${w} ${w===1?(C==null?void 0:C.singular)||"item":(C==null?void 0:C.plural)||"items"} selected`}),Z()]})}):P.headers.map((k,I)=>f.jsx("th",{className:F(Ke({alignment:"start",sortable:k.column.getCanSort(),sticky:u&&I===P.headers.length-1})),children:k.isPlaceholder?null:No(k.column.columnDef.header,k.getContext())},k.id))]},P.id))}):f.jsxs("thead",{children:[$&&f.jsxs("tr",{children:[l&&f.jsx("th",{className:F(Ke({isSelectionColumn:!0})),children:f.jsx(Dt,{checked:w==="All"||w===y&&y>0?!0:typeof w=="number"&&w>0?"indeterminate":!1,onChange:P=>{R&&R("page",P,void 0,void 0)},"aria-label":`Select all ${(C==null?void 0:C.plural)||"items"}`})}),f.jsx("th",{colSpan:e.length,className:F(Ke({alignment:"start"}),"text-left"),children:f.jsxs("div",{className:"flex items-center justify-between w-full",children:[f.jsx(ye,{variant:"bodyMd",as:"span",children:w==="All"?`All ${(C==null?void 0:C.plural)||"items"} selected`:`${w} ${w===1?(C==null?void 0:C.singular)||"item":(C==null?void 0:C.plural)||"items"} selected`}),Z()]})})]}),f.jsxs("tr",{children:[l&&!$&&f.jsx("th",{className:F(Ke({isSelectionColumn:!0})),children:f.jsx(Dt,{checked:w==="All"||w===y&&y>0?!0:typeof w=="number"&&w>0?"indeterminate":!1,onChange:P=>{R&&R("page",P,void 0,void 0)},"aria-label":`Select all ${(C==null?void 0:C.plural)||"items"}`})}),$&&l&&f.jsx("th",{className:F(Ke({isSelectionColumn:!0}))}),e.map((P,k)=>{const I=(d==null?void 0:d[k])||!1,X=p===k,te=X?g:void 0,U=()=>X?te==="ascending"?"descending":"ascending":"descending",J=()=>!I||!X||!te?null:te==="descending"?f.jsx(lr,{className:"ml-1 h-4 w-4"}):f.jsx(sr,{className:"ml-1 h-4 w-4"}),re=()=>{if(!I||!b)return;const ue=U();b(k,ue)};return f.jsx("th",{className:F(Ke({alignment:P.alignment||"start",sortable:I,sticky:u&&k===e.length-1}),I&&"cursor-pointer hover:bg-gray-50"),onClick:re,children:f.jsxs("div",{className:"flex items-center",children:[f.jsx(ye,{variant:"bodyMd",fontWeight:"semibold",children:(typeof P.title=="string",P.title)}),J()]})},k)})]})]})},j=()=>{var Z;if(N)return f.jsx("tbody",{children:(Z=N.getRowModel().rows)!=null&&Z.length?N.getRowModel().rows.map(P=>f.jsx("tr",{className:"hover:bg-gray-50",children:P.getVisibleCells().map((k,I)=>f.jsx("td",{className:F(Xa({alignment:"start",sticky:u&&I===P.getVisibleCells().length-1})),children:No(k.column.columnDef.cell,k.getContext())},k.id))},P.id)):f.jsx("tr",{children:f.jsx("td",{colSpan:(A==null?void 0:A.length)||e.length,className:"h-24 text-center",children:o||f.jsx(ye,{variant:"bodyMd",tone:"subdued",children:"No results."})})})});const $=m.Children.map(r,P=>m.isValidElement(P)&&P.type===Tr?m.cloneElement(P,{selectable:l,onSelectionChange:R,resourceName:C,...P.props}):P);return f.jsx("tbody",{children:$})};return y===0&&o?f.jsx("div",{className:"flex items-center justify-center p-8",children:o}):f.jsxs("div",{className:"overflow-x-auto",children:[f.jsxs("table",{ref:q,className:F(qa({condensed:E,hasZebraStriping:v,loading:_,hasIndexFilters:G}),O),...z,children:[B(),j()]}),x&&f.jsx("div",{className:"flex justify-center py-4",children:f.jsx(Fr,{type:"table",...x})})]})});Ya.displayName="IndexTable";const Tr=m.forwardRef(({children:e,id:t,selected:n=!1,position:r,tone:o,disabled:s=!1,selectionRange:a,rowType:i="data",accessibilityLabel:u,onClick:l,onNavigation:d,className:c,selectable:g=!0,onSelectionChange:p,resourceName:b,...h},v)=>{const x=Q("hover:bg-[#f7fafc] transition-colors border-b border-[#e3e3e3] last:border-b-0 ",{variants:{selected:{true:"bg-blue-50",indeterminate:"bg-blue-25",false:""},tone:{subdued:"opacity-60",success:"bg-green-50",warning:"bg-yellow-50",critical:"bg-red-50"},disabled:{true:"opacity-50 pointer-events-none",false:""},rowType:{data:"",subheader:"bg-gray-100 font-semibold",child:"pl-8"}},defaultVariants:{selected:!1,disabled:!1,rowType:"data"}}),y=w=>{const C=w.target;C.type==="checkbox"||C.closest('input[type="checkbox"]')||C.closest("button")||C.closest("a")||(g&&p&&!s&&p("single",!0,t,r),l&&l())};return f.jsxs("tr",{ref:v,className:F(x({selected:n===!0?!0:n==="indeterminate"?"indeterminate":!1,tone:o,disabled:s,rowType:i}),g&&!s&&"cursor-pointer",c),onClick:y,...h,children:[g&&f.jsx("td",{className:F(Xa({isSelectionColumn:!0})),children:f.jsx(Dt,{checked:n===!0,onChange:w=>{p&&p("single",w,t,r)},disabled:s,"aria-label":u||`Select ${(b==null?void 0:b.singular)||"item"}`})}),e]})});Tr.displayName="IndexTable.Row";const Za=m.forwardRef(({as:e="td",id:t,children:n,className:r,flush:o=!1,colSpan:s,scope:a,headers:i,...u},l)=>{const d=Q("border-gray-200 text-xs",{variants:{flush:{true:"",false:"p-2"}},defaultVariants:{flush:!1}});return f.jsx(e,{ref:l,id:t,className:F(d({flush:o}),r),colSpan:s,scope:a,headers:i,...u,children:n})});Za.displayName="IndexTable.Cell";const ff=(e,t)=>{const[n,r]=m.useState((t==null?void 0:t.selectedResources)||[]),[o,s]=m.useState((t==null?void 0:t.allResourcesSelected)||!1),a=m.useCallback((u,l,d,c)=>{if(u==="single"&&typeof d=="string")r(l?g=>g.includes(d)?g.filter(p=>p!==d):[...g,d]:[d]);else if(u==="multiple"&&typeof d=="object"){const{start:g,end:p}=d,b=e.slice(g,p+1).map(h=>h.id);r(l?h=>[...new Set([...h,...b])]:b)}else if(u==="page")if(l){const g=e.map(p=>p.id);r(g),s(!1)}else r([]),s(!1);else u==="all"&&(s(l),l&&r([]))},[e]),i=m.useCallback(()=>{r([]),s(!1)},[]);return{selectedResources:n,allResourcesSelected:o,handleSelectionChange:a,clearSelection:i}},Vr=Ya;Vr.Row=Tr;Vr.Cell=Za;const Ka=Q("inline-block shrink-0 w-5 h-5",{variants:{tone:{base:"!text-white",inherit:"!text-inherit",subdued:"!text-muted-foreground",caution:"!text-amber-600",warning:"text-orange-600",critical:"text-destructive",interactive:"text-primary",info:"text-blue-600",success:"text-green-600",primary:"text-primary",emphasis:"text-foreground font-semibold",magic:"text-purple-600",textCaution:"text-amber-700",textWarning:"text-orange-700",textCritical:"text-red-700",textInfo:"text-blue-700",textSuccess:"text-green-700",textPrimary:"text-primary",textMagic:"text-purple-700"}},defaultVariants:{tone:"base"}}),Lr=m.forwardRef(({source:e,tone:t="base",accessibilityLabel:n,className:r,...o},s)=>{const a=F(Ka({tone:t}),r),i=m.useMemo(()=>{var l;if(!e)return null;if(typeof e=="function"){const d=e;return f.jsx(d,{className:a})}return m.isValidElement(e)?m.cloneElement(e,{className:F(a,(l=e.props)==null?void 0:l.className)}):typeof e=="string"?f.jsx("div",{className:a,dangerouslySetInnerHTML:{__html:e}}):e},[e,a]),u=n?{"aria-label":n}:{"aria-hidden":!0};return f.jsx("span",{ref:s,className:"inline-block shrink-0 w-5 h-5",...u,...o,children:i})});Lr.displayName="Icon";const Qa=Q("fixed top-0 left-1/2 transform -translate-x-1/2 z-50 w-1/2 bg-[#303030] text-white shadow-lg border border-[#2c2c2c] font-inter transition-transform duration-200 ease-in-out rounded-[40px] p-0",{variants:{open:{true:"translate-y-0",false:"-translate-y-full"}},defaultVariants:{open:!1}}),gf=ma,pf=ha,Ja=m.forwardRef(({className:e,...t},n)=>f.jsx(kr,{ref:n,className:F("fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));Ja.displayName=kr.displayName;const ei=m.forwardRef(({className:e,children:t,...n},r)=>f.jsxs(pf,{children:[f.jsx(Ja,{}),f.jsx(Ir,{ref:r,className:F("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg font-inter rounded-[40px] border border-[#e3e3e3] bg-[#303030] text-white",e),...n,children:t})]}));ei.displayName=Ir.displayName;const ti=m.forwardRef(({className:e,...t},n)=>f.jsx(ba,{ref:n,className:F("text-lg font-semibold leading-none tracking-tight",e),...t}));ti.displayName=ba.displayName;const ni=m.forwardRef(({className:e,...t},n)=>f.jsx(va,{ref:n,className:F("text-sm text-gray-600",e),...t}));ni.displayName=va.displayName;const ri=m.forwardRef(({id:e,children:t,discardConfirmation:n=!1,open:r=!1,className:o,onSave:s,onDiscard:a,saveText:i="Save",discardText:u="Discard",saveLoading:l=!1,discardLoading:d=!1,...c},g)=>{const[p,b]=m.useState(!1),[h,v]=m.useState(null),x=!!(s||a),y=m.useCallback(E=>{n?(v(()=>E),b(!0)):E()},[n]),w=m.useMemo(()=>x||!t?null:m.Children.map(t,E=>{if(m.isValidElement(E)&&(E.type==="button"||E.type===oe)){const R=E.props;if(R.variant==="primary"&&n&&typeof R.onClick=="function")return m.cloneElement(E,{...R,onClick:O=>{O.preventDefault(),y(R.onClick)}})}return E}),[t,n,x,y]),C=m.useMemo(()=>x?f.jsxs(f.Fragment,{children:[a&&f.jsx(oe,{variant:"primary",loading:d,onClick:()=>y(a),children:u}),s&&f.jsx(oe,{loading:l,onClick:s,children:i})]}):null,[x,a,s,d,l,u,i,y]),_=m.useCallback(()=>{b(!1),h&&(h(),v(null))},[h]),M=m.useCallback(()=>{b(!1)},[]);return f.jsxs(f.Fragment,{children:[f.jsx("div",{ref:g,id:e,className:F(Qa({open:r}),o),...c,children:f.jsxs("div",{className:"flex items-center justify-between p-2",children:[f.jsxs("div",{className:"flex items-center !justify-center gap-1",children:[f.jsx(Lr,{source:To,tone:"base"}),f.jsx("span",{className:"text-sm font-medium",children:"Unsaved changes"})]}),f.jsx("div",{className:"flex items-center gap-3",children:x?C:w})]})}),f.jsx(gf,{open:p,onOpenChange:b,children:f.jsxs(ei,{className:"p-0 overflow-hidden bg-white",children:[f.jsx("div",{className:"px-6 py-4 bg-[#e3e3e3] border-b border-gray-200",children:f.jsx(ti,{className:"text-lg font-semibold text-gray-900 leading-6",children:"Discard changes"})}),f.jsx("div",{className:"px-6 py-4",children:f.jsx(ni,{className:"text-sm text-gray-600",children:"Are you sure you want to discard your changes? This action cannot be undone."})}),f.jsxs("div",{className:"border-t border-gray-200 px-6 py-4 bg-white flex justify-end gap-3",children:[f.jsx(oe,{onClick:M,children:"Cancel"}),f.jsx(oe,{variant:"primary",tone:"critical",onClick:_,children:"Discard changes"})]})]})})]})});ri.displayName="ContextualSaveBar";function mf(e){if(typeof document>"u")return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}const hf=e=>{switch(e){case"success":return xf;case"info":return wf;case"warning":return yf;case"error":return Cf;default:return null}},bf=Array(12).fill(0),vf=({visible:e,className:t})=>S.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},S.createElement("div",{className:"sonner-spinner"},bf.map((n,r)=>S.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${r}`})))),xf=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},S.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),yf=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},S.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),wf=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},S.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),Cf=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},S.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Sf=S.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},S.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),S.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),_f=()=>{const[e,t]=S.useState(document.hidden);return S.useEffect(()=>{const n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e};let rr=1;class Rf{constructor(){this.subscribe=t=>(this.subscribers.push(t),()=>{const n=this.subscribers.indexOf(t);this.subscribers.splice(n,1)}),this.publish=t=>{this.subscribers.forEach(n=>n(t))},this.addToast=t=>{this.publish(t),this.toasts=[...this.toasts,t]},this.create=t=>{var n;const{message:r,...o}=t,s=typeof(t==null?void 0:t.id)=="number"||((n=t.id)==null?void 0:n.length)>0?t.id:rr++,a=this.toasts.find(u=>u.id===s),i=t.dismissible===void 0?!0:t.dismissible;return this.dismissedToasts.has(s)&&this.dismissedToasts.delete(s),a?this.toasts=this.toasts.map(u=>u.id===s?(this.publish({...u,...t,id:s,title:r}),{...u,...t,id:s,dismissible:i,title:r}):u):this.addToast({title:r,...o,dismissible:i,id:s}),s},this.dismiss=t=>(t?(this.dismissedToasts.add(t),requestAnimationFrame(()=>this.subscribers.forEach(n=>n({id:t,dismiss:!0})))):this.toasts.forEach(n=>{this.subscribers.forEach(r=>r({id:n.id,dismiss:!0}))}),t),this.message=(t,n)=>this.create({...n,message:t}),this.error=(t,n)=>this.create({...n,message:t,type:"error"}),this.success=(t,n)=>this.create({...n,type:"success",message:t}),this.info=(t,n)=>this.create({...n,type:"info",message:t}),this.warning=(t,n)=>this.create({...n,type:"warning",message:t}),this.loading=(t,n)=>this.create({...n,type:"loading",message:t}),this.promise=(t,n)=>{if(!n)return;let r;n.loading!==void 0&&(r=this.create({...n,promise:t,type:"loading",message:n.loading,description:typeof n.description!="function"?n.description:void 0}));const o=Promise.resolve(t instanceof Function?t():t);let s=r!==void 0,a;const i=o.then(async l=>{if(a=["resolve",l],S.isValidElement(l))s=!1,this.create({id:r,type:"default",message:l});else if(Pf(l)&&!l.ok){s=!1;const c=typeof n.error=="function"?await n.error(`HTTP error! status: ${l.status}`):n.error,g=typeof n.description=="function"?await n.description(`HTTP error! status: ${l.status}`):n.description,b=typeof c=="object"&&!S.isValidElement(c)?c:{message:c};this.create({id:r,type:"error",description:g,...b})}else if(l instanceof Error){s=!1;const c=typeof n.error=="function"?await n.error(l):n.error,g=typeof n.description=="function"?await n.description(l):n.description,b=typeof c=="object"&&!S.isValidElement(c)?c:{message:c};this.create({id:r,type:"error",description:g,...b})}else if(n.success!==void 0){s=!1;const c=typeof n.success=="function"?await n.success(l):n.success,g=typeof n.description=="function"?await n.description(l):n.description,b=typeof c=="object"&&!S.isValidElement(c)?c:{message:c};this.create({id:r,type:"success",description:g,...b})}}).catch(async l=>{if(a=["reject",l],n.error!==void 0){s=!1;const d=typeof n.error=="function"?await n.error(l):n.error,c=typeof n.description=="function"?await n.description(l):n.description,p=typeof d=="object"&&!S.isValidElement(d)?d:{message:d};this.create({id:r,type:"error",description:c,...p})}}).finally(()=>{s&&(this.dismiss(r),r=void 0),n.finally==null||n.finally.call(n)}),u=()=>new Promise((l,d)=>i.then(()=>a[0]==="reject"?d(a[1]):l(a[1])).catch(d));return typeof r!="string"&&typeof r!="number"?{unwrap:u}:Object.assign(r,{unwrap:u})},this.custom=(t,n)=>{const r=(n==null?void 0:n.id)||rr++;return this.create({jsx:t(r),id:r,...n}),r},this.getActiveToasts=()=>this.toasts.filter(t=>!this.dismissedToasts.has(t.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}}const he=new Rf,Ef=(e,t)=>{const n=(t==null?void 0:t.id)||rr++;return he.addToast({title:e,...t,id:n}),n},Pf=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",Mf=Ef,Nf=()=>he.toasts,Af=()=>he.getActiveToasts(),Ao=Object.assign(Mf,{success:he.success,info:he.info,warning:he.warning,error:he.error,custom:he.custom,message:he.message,promise:he.promise,dismiss:he.dismiss,loading:he.loading},{getHistory:Nf,getToasts:Af});mf("[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}");function an(e){return e.label!==void 0}const jf=3,kf="24px",If="16px",jo=4e3,Ff=356,$f=14,Of=45,Df=200;function Ie(...e){return e.filter(Boolean).join(" ")}function Tf(e){const[t,n]=e.split("-"),r=[];return t&&r.push(t),n&&r.push(n),r}const Vf=e=>{var t,n,r,o,s,a,i,u,l;const{invert:d,toast:c,unstyled:g,interacting:p,setHeights:b,visibleToasts:h,heights:v,index:x,toasts:y,expanded:w,removeToast:C,defaultRichColors:_,closeButton:M,style:E,cancelButtonStyle:R,actionButtonStyle:O,className:T="",descriptionClassName:D="",duration:A,position:G,gap:z,expandByDefault:q,classNames:N,icons:B,closeButtonAriaLabel:j="Close toast"}=e,[$,Z]=S.useState(null),[P,k]=S.useState(null),[I,X]=S.useState(!1),[te,U]=S.useState(!1),[J,re]=S.useState(!1),[ue,pe]=S.useState(!1),[_e,K]=S.useState(!1),[le,ve]=S.useState(0),[zt,We]=S.useState(0),Re=S.useRef(c.duration||A||jo),pt=S.useRef(null),de=S.useRef(null),st=x===0,En=x+1<=h,Y=c.type,Ue=c.dismissible!==!1,Ht=c.className||"",At=c.descriptionClassName||"",at=S.useMemo(()=>v.findIndex(H=>H.toastId===c.id)||0,[v,c.id]),Gt=S.useMemo(()=>{var H;return(H=c.closeButton)!=null?H:M},[c.closeButton,M]),it=S.useMemo(()=>c.duration||A||jo,[c.duration,A]),lt=S.useRef(0),qe=S.useRef(0),Wt=S.useRef(0),Ve=S.useRef(null),[Pn,Mn]=G.split("-"),Ut=S.useMemo(()=>v.reduce((H,ae,ce)=>ce>=at?H:H+ae.height,0),[v,at]),Xe=_f(),ct=c.invert||d,mt=Y==="loading";qe.current=S.useMemo(()=>at*z+Ut,[at,Ut]),S.useEffect(()=>{Re.current=it},[it]),S.useEffect(()=>{X(!0)},[]),S.useEffect(()=>{const H=de.current;if(H){const ae=H.getBoundingClientRect().height;return We(ae),b(ce=>[{toastId:c.id,height:ae,position:c.position},...ce]),()=>b(ce=>ce.filter(fe=>fe.toastId!==c.id))}},[b,c.id]),S.useLayoutEffect(()=>{if(!I)return;const H=de.current,ae=H.style.height;H.style.height="auto";const ce=H.getBoundingClientRect().height;H.style.height=ae,We(ce),b(fe=>fe.find(ie=>ie.toastId===c.id)?fe.map(ie=>ie.toastId===c.id?{...ie,height:ce}:ie):[{toastId:c.id,height:ce,position:c.position},...fe])},[I,c.title,c.description,b,c.id,c.jsx,c.action,c.cancel]);const Le=S.useCallback(()=>{U(!0),ve(qe.current),b(H=>H.filter(ae=>ae.toastId!==c.id)),setTimeout(()=>{C(c)},Df)},[c,C,b,qe]);S.useEffect(()=>{if(c.promise&&Y==="loading"||c.duration===1/0||c.type==="loading")return;let H;return w||p||Xe?(()=>{if(Wt.current<lt.current){const fe=new Date().getTime()-lt.current;Re.current=Re.current-fe}Wt.current=new Date().getTime()})():(()=>{Re.current!==1/0&&(lt.current=new Date().getTime(),H=setTimeout(()=>{c.onAutoClose==null||c.onAutoClose.call(c,c),Le()},Re.current))})(),()=>clearTimeout(H)},[w,p,c,Y,Xe,Le]),S.useEffect(()=>{c.delete&&(Le(),c.onDismiss==null||c.onDismiss.call(c,c))},[Le,c.delete]);function qt(){var H;if(B!=null&&B.loading){var ae;return S.createElement("div",{className:Ie(N==null?void 0:N.loader,c==null||(ae=c.classNames)==null?void 0:ae.loader,"sonner-loader"),"data-visible":Y==="loading"},B.loading)}return S.createElement(vf,{className:Ie(N==null?void 0:N.loader,c==null||(H=c.classNames)==null?void 0:H.loader),visible:Y==="loading"})}const Xt=c.icon||(B==null?void 0:B[Y])||hf(Y);var jt,kt;return S.createElement("li",{tabIndex:0,ref:de,className:Ie(T,Ht,N==null?void 0:N.toast,c==null||(t=c.classNames)==null?void 0:t.toast,N==null?void 0:N.default,N==null?void 0:N[Y],c==null||(n=c.classNames)==null?void 0:n[Y]),"data-sonner-toast":"","data-rich-colors":(jt=c.richColors)!=null?jt:_,"data-styled":!(c.jsx||c.unstyled||g),"data-mounted":I,"data-promise":!!c.promise,"data-swiped":_e,"data-removed":te,"data-visible":En,"data-y-position":Pn,"data-x-position":Mn,"data-index":x,"data-front":st,"data-swiping":J,"data-dismissible":Ue,"data-type":Y,"data-invert":ct,"data-swipe-out":ue,"data-swipe-direction":P,"data-expanded":!!(w||q&&I),style:{"--index":x,"--toasts-before":x,"--z-index":y.length-x,"--offset":`${te?le:qe.current}px`,"--initial-height":q?"auto":`${zt}px`,...E,...c.style},onDragEnd:()=>{re(!1),Z(null),Ve.current=null},onPointerDown:H=>{H.button!==2&&(mt||!Ue||(pt.current=new Date,ve(qe.current),H.target.setPointerCapture(H.pointerId),H.target.tagName!=="BUTTON"&&(re(!0),Ve.current={x:H.clientX,y:H.clientY})))},onPointerUp:()=>{var H,ae,ce;if(ue||!Ue)return;Ve.current=null;const fe=Number(((H=de.current)==null?void 0:H.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),ut=Number(((ae=de.current)==null?void 0:ae.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),ie=new Date().getTime()-((ce=pt.current)==null?void 0:ce.getTime()),me=$==="x"?fe:ut,ee=Math.abs(me)/ie;if(Math.abs(me)>=Of||ee>.11){ve(qe.current),c.onDismiss==null||c.onDismiss.call(c,c),k($==="x"?fe>0?"right":"left":ut>0?"down":"up"),Le(),pe(!0);return}else{var se,Ee;(se=de.current)==null||se.style.setProperty("--swipe-amount-x","0px"),(Ee=de.current)==null||Ee.style.setProperty("--swipe-amount-y","0px")}K(!1),re(!1),Z(null)},onPointerMove:H=>{var ae,ce,fe;if(!Ve.current||!Ue||((ae=window.getSelection())==null?void 0:ae.toString().length)>0)return;const ie=H.clientY-Ve.current.y,me=H.clientX-Ve.current.x;var ee;const se=(ee=e.swipeDirections)!=null?ee:Tf(G);!$&&(Math.abs(me)>1||Math.abs(ie)>1)&&Z(Math.abs(me)>Math.abs(ie)?"x":"y");let Ee={x:0,y:0};const Br=dt=>1/(1.5+Math.abs(dt)/20);if($==="y"){if(se.includes("top")||se.includes("bottom"))if(se.includes("top")&&ie<0||se.includes("bottom")&&ie>0)Ee.y=ie;else{const dt=ie*Br(ie);Ee.y=Math.abs(dt)<Math.abs(ie)?dt:ie}}else if($==="x"&&(se.includes("left")||se.includes("right")))if(se.includes("left")&&me<0||se.includes("right")&&me>0)Ee.x=me;else{const dt=me*Br(me);Ee.x=Math.abs(dt)<Math.abs(me)?dt:me}(Math.abs(Ee.x)>0||Math.abs(Ee.y)>0)&&K(!0),(ce=de.current)==null||ce.style.setProperty("--swipe-amount-x",`${Ee.x}px`),(fe=de.current)==null||fe.style.setProperty("--swipe-amount-y",`${Ee.y}px`)}},Gt&&!c.jsx&&Y!=="loading"?S.createElement("button",{"aria-label":j,"data-disabled":mt,"data-close-button":!0,onClick:mt||!Ue?()=>{}:()=>{Le(),c.onDismiss==null||c.onDismiss.call(c,c)},className:Ie(N==null?void 0:N.closeButton,c==null||(r=c.classNames)==null?void 0:r.closeButton)},(kt=B==null?void 0:B.close)!=null?kt:Sf):null,(Y||c.icon||c.promise)&&c.icon!==null&&((B==null?void 0:B[Y])!==null||c.icon)?S.createElement("div",{"data-icon":"",className:Ie(N==null?void 0:N.icon,c==null||(o=c.classNames)==null?void 0:o.icon)},c.promise||c.type==="loading"&&!c.icon?c.icon||qt():null,c.type!=="loading"?Xt:null):null,S.createElement("div",{"data-content":"",className:Ie(N==null?void 0:N.content,c==null||(s=c.classNames)==null?void 0:s.content)},S.createElement("div",{"data-title":"",className:Ie(N==null?void 0:N.title,c==null||(a=c.classNames)==null?void 0:a.title)},c.jsx?c.jsx:typeof c.title=="function"?c.title():c.title),c.description?S.createElement("div",{"data-description":"",className:Ie(D,At,N==null?void 0:N.description,c==null||(i=c.classNames)==null?void 0:i.description)},typeof c.description=="function"?c.description():c.description):null),S.isValidElement(c.cancel)?c.cancel:c.cancel&&an(c.cancel)?S.createElement("button",{"data-button":!0,"data-cancel":!0,style:c.cancelButtonStyle||R,onClick:H=>{an(c.cancel)&&Ue&&(c.cancel.onClick==null||c.cancel.onClick.call(c.cancel,H),Le())},className:Ie(N==null?void 0:N.cancelButton,c==null||(u=c.classNames)==null?void 0:u.cancelButton)},c.cancel.label):null,S.isValidElement(c.action)?c.action:c.action&&an(c.action)?S.createElement("button",{"data-button":!0,"data-action":!0,style:c.actionButtonStyle||O,onClick:H=>{an(c.action)&&(c.action.onClick==null||c.action.onClick.call(c.action,H),!H.defaultPrevented&&Le())},className:Ie(N==null?void 0:N.actionButton,c==null||(l=c.classNames)==null?void 0:l.actionButton)},c.action.label):null)};function ko(){if(typeof window>"u"||typeof document>"u")return"ltr";const e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function Lf(e,t){const n={};return[e,t].forEach((r,o)=>{const s=o===1,a=s?"--mobile-offset":"--offset",i=s?If:kf;function u(l){["top","right","bottom","left"].forEach(d=>{n[`${a}-${d}`]=typeof l=="number"?`${l}px`:l})}typeof r=="number"||typeof r=="string"?u(r):typeof r=="object"?["top","right","bottom","left"].forEach(l=>{r[l]===void 0?n[`${a}-${l}`]=i:n[`${a}-${l}`]=typeof r[l]=="number"?`${r[l]}px`:r[l]}):u(i)}),n}const Bf=S.forwardRef(function(t,n){const{invert:r,position:o="bottom-right",hotkey:s=["altKey","KeyT"],expand:a,closeButton:i,className:u,offset:l,mobileOffset:d,theme:c="light",richColors:g,duration:p,style:b,visibleToasts:h=jf,toastOptions:v,dir:x=ko(),gap:y=$f,icons:w,containerAriaLabel:C="Notifications"}=t,[_,M]=S.useState([]),E=S.useMemo(()=>Array.from(new Set([o].concat(_.filter(P=>P.position).map(P=>P.position)))),[_,o]),[R,O]=S.useState([]),[T,D]=S.useState(!1),[A,G]=S.useState(!1),[z,q]=S.useState(c!=="system"?c:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),N=S.useRef(null),B=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),j=S.useRef(null),$=S.useRef(!1),Z=S.useCallback(P=>{M(k=>{var I;return(I=k.find(X=>X.id===P.id))!=null&&I.delete||he.dismiss(P.id),k.filter(({id:X})=>X!==P.id)})},[]);return S.useEffect(()=>he.subscribe(P=>{if(P.dismiss){requestAnimationFrame(()=>{M(k=>k.map(I=>I.id===P.id?{...I,delete:!0}:I))});return}setTimeout(()=>{or.flushSync(()=>{M(k=>{const I=k.findIndex(X=>X.id===P.id);return I!==-1?[...k.slice(0,I),{...k[I],...P},...k.slice(I+1)]:[P,...k]})})})}),[_]),S.useEffect(()=>{if(c!=="system"){q(c);return}if(c==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?q("dark"):q("light")),typeof window>"u")return;const P=window.matchMedia("(prefers-color-scheme: dark)");try{P.addEventListener("change",({matches:k})=>{q(k?"dark":"light")})}catch{P.addListener(({matches:I})=>{try{q(I?"dark":"light")}catch(X){console.error(X)}})}},[c]),S.useEffect(()=>{_.length<=1&&D(!1)},[_]),S.useEffect(()=>{const P=k=>{var I;if(s.every(U=>k[U]||k.code===U)){var te;D(!0),(te=N.current)==null||te.focus()}k.code==="Escape"&&(document.activeElement===N.current||(I=N.current)!=null&&I.contains(document.activeElement))&&D(!1)};return document.addEventListener("keydown",P),()=>document.removeEventListener("keydown",P)},[s]),S.useEffect(()=>{if(N.current)return()=>{j.current&&(j.current.focus({preventScroll:!0}),j.current=null,$.current=!1)}},[N.current]),S.createElement("section",{ref:n,"aria-label":`${C} ${B}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},E.map((P,k)=>{var I;const[X,te]=P.split("-");return _.length?S.createElement("ol",{key:P,dir:x==="auto"?ko():x,tabIndex:-1,ref:N,className:u,"data-sonner-toaster":!0,"data-sonner-theme":z,"data-y-position":X,"data-x-position":te,style:{"--front-toast-height":`${((I=R[0])==null?void 0:I.height)||0}px`,"--width":`${Ff}px`,"--gap":`${y}px`,...b,...Lf(l,d)},onBlur:U=>{$.current&&!U.currentTarget.contains(U.relatedTarget)&&($.current=!1,j.current&&(j.current.focus({preventScroll:!0}),j.current=null))},onFocus:U=>{U.target instanceof HTMLElement&&U.target.dataset.dismissible==="false"||$.current||($.current=!0,j.current=U.relatedTarget)},onMouseEnter:()=>D(!0),onMouseMove:()=>D(!0),onMouseLeave:()=>{A||D(!1)},onDragEnd:()=>D(!1),onPointerDown:U=>{U.target instanceof HTMLElement&&U.target.dataset.dismissible==="false"||G(!0)},onPointerUp:()=>G(!1)},_.filter(U=>!U.position&&k===0||U.position===P).map((U,J)=>{var re,ue;return S.createElement(Vf,{key:U.id,icons:w,index:J,toast:U,defaultRichColors:g,duration:(re=v==null?void 0:v.duration)!=null?re:p,className:v==null?void 0:v.className,descriptionClassName:v==null?void 0:v.descriptionClassName,invert:r,visibleToasts:h,closeButton:(ue=v==null?void 0:v.closeButton)!=null?ue:i,interacting:A,position:P,style:v==null?void 0:v.style,unstyled:v==null?void 0:v.unstyled,classNames:v==null?void 0:v.classNames,cancelButtonStyle:v==null?void 0:v.cancelButtonStyle,actionButtonStyle:v==null?void 0:v.actionButtonStyle,closeButtonAriaLabel:v==null?void 0:v.closeButtonAriaLabel,removeToast:Z,toasts:_.filter(pe=>pe.position==U.position),heights:R.filter(pe=>pe.position==U.position),setHeights:O,expandByDefault:a,gap:y,expanded:T,swipeDirections:t.swipeDirections})})):null}))}),oi=Q("font-inter text-sm font-medium rounded-lg border shadow-lg !w-fit !max-w-md",{variants:{variant:{default:"bg-[#0d1213] border-gray-700 text-white",success:"bg-[#0d1213] border-green-600 text-green-200",error:"bg-[#0d1213] border-red-600 text-red-200",warning:"bg-[#0d1213] border-yellow-600 text-yellow-200",info:"bg-[#0d1213] border-blue-600 text-blue-200"}},defaultVariants:{variant:"default"}}),si=m.forwardRef(({className:e,theme:t="light",position:n="bottom-center",visibleToasts:r=3,closeButton:o=!0,richColors:s=!1,expand:a=!1,gap:i=14,offset:u="20px",...l},d)=>f.jsx(Bf,{ref:d,className:F("toaster group",e),theme:t,position:n,visibleToasts:r,closeButton:o,richColors:s,expand:a,gap:i,offset:u,toastOptions:{classNames:{toast:F(oi({variant:"default"}),"group toast flex items-start justify-between gap-4 p-4"),description:"group-[.toast]:text-gray-300",actionButton:"group-[.toast]:bg-white group-[.toast]:text-[#0d1213] group-[.toast]:hover:bg-gray-200 group-[.toast]:rounded group-[.toast]:px-3 group-[.toast]:py-1 group-[.toast]:text-sm group-[.toast]:font-medium",cancelButton:"group-[.toast]:bg-gray-700 group-[.toast]:text-white group-[.toast]:hover:bg-gray-600 group-[.toast]:rounded group-[.toast]:px-3 group-[.toast]:py-1 group-[.toast]:text-sm group-[.toast]:font-medium",closeButton:"group-[.toast]:ml-auto group-[.toast]:bg-transparent group-[.toast]:border-0 group-[.toast]:text-white group-[.toast]:hover:bg-gray-700 group-[.toast]:hover:text-white group-[.toast]:p-1"}},...l}));si.displayName="Toaster";const zf=()=>({show:(e,t={})=>{const{duration:n=4e3,action:r,onDismiss:o,dismissible:s=!0}=t,a=Ao(e,{duration:n,dismissible:s,action:r?{label:r.label,onClick:r.onClick}:void 0,onDismiss:o});return String(a)},hide:e=>{Ao.dismiss(e)}}),Hf=zf();exports.Badge=Aa;exports.Bleed=Ea;exports.BlockStack=Fa;exports.Box=es;exports.Button=oe;exports.ButtonGroup=dr;exports.Card=Js;exports.ChoiceList=Mr;exports.ContextualSaveBar=ri;exports.Icon=Lr;exports.IndexFilters=Da;exports.IndexTable=Vr;exports.InlineStack=ka;exports.Modal=Ca;exports.Page=Ma;exports.Pagination=Fr;exports.Popover=Sn;exports.Text=ye;exports.TextField=fr;exports.TitleBar=_a;exports.Toaster=si;exports.badgeVariants=md;exports.bleedVariants=Ra;exports.blockStackVariants=Ia;exports.boxVariants=Jo;exports.buttonGroupVariants=Qo;exports.buttonVariants=Ko;exports.cn=F;exports.iconVariants=Ka;exports.indexFiltersVariants=Oa;exports.indexTableVariants=qa;exports.inlineStackVariants=ja;exports.modalVariants=wa;exports.paginationVariants=$a;exports.polarisCardVariants=Qs;exports.polarisChoiceListVariants=Ks;exports.polarisPageVariants=Pa;exports.polarisPopoverVariants=Zs;exports.saveBarVariants=Qa;exports.textFieldVariants=ns;exports.textVariants=ts;exports.titleBarVariants=Sa;exports.toast=Hf;exports.toastVariants=oi;exports.useIndexResourceState=ff;exports.useSetIndexFiltersMode=vd;
//# sourceMappingURL=index.cjs.map
