import { Meta } from "@storybook/addon-docs/blocks";

<Meta title="Getting Started with Storeware Polaris" />

<div className="sb-container">
  <div className='sb-section-title'>
    # @storeware/polaris Component Library

    A React component library that provides 100% API compatibility with Shopify's Polaris design system while being built on shadcn/ui foundation with Tailwind CSS. Explore our components and learn how to integrate them into your project.

  </div>
  
  <div className="sb-section">
    <div className="sb-section-item">
      <h4 className="sb-section-item-heading">Installation & Setup</h4>
      <p className="sb-section-item-paragraph">Get started by installing @storeware/polaris and configuring Tailwind CSS in your project. Import styles and start using Polaris-compatible components immediately.</p>
      <a href="https://github.com/storeware/polaris#installation" target="_blank">
        View installation guide →
      </a>
    </div>
    
    <div className="sb-section-item">
      <h4 className="sb-section-item-heading">100% Polaris Compatible</h4>
      <p className="sb-section-item-paragraph">Drop-in replacement for Shopify Polaris components. Same API, same behavior, enhanced with modern React patterns and TypeScript support.</p>
      <a href="https://polaris.shopify.com/" target="_blank">
        Shopify Polaris docs →
      </a>
    </div>
    
    <div className="sb-section-item">
      <h4 className="sb-section-item-heading">Available Components</h4>
      <p className="sb-section-item-paragraph">Explore Button, ButtonGroup, Box, and more components. Each component includes comprehensive documentation, examples, and TypeScript definitions.</p>
      <a href="?path=/docs/components-button--docs" target="_blank">
        Browse components →
      </a>
    </div>
  </div>
</div>

<div className="sb-container">
  <div className='sb-section-title'>
    # Component Features

    Built with modern React patterns, TypeScript-first approach, and enhanced customization capabilities while maintaining perfect Polaris compatibility.

  </div>

  <div className="sb-section">
    <div className="sb-features-grid">
      <div className="sb-grid-item">
        <h4 className="sb-section-item-heading">TypeScript First</h4>
        <p className="sb-section-item-paragraph">Full type safety with excellent IntelliSense support. All components include comprehensive TypeScript definitions and JSDoc comments.</p>
        <a href="?path=/docs/components-button--docs" target="_blank">
          View component docs →
        </a>
      </div>
      
      <div className="sb-grid-item">
        <h4 className="sb-section-item-heading">Tailwind CSS Powered</h4>
        <p className="sb-section-item-paragraph">Built on Tailwind CSS for easy customization. Override styles, create themes, and extend components while maintaining Polaris design tokens.</p>
        <a href="https://tailwindcss.com/docs" target="_blank">
          Tailwind CSS docs →
        </a>
      </div>
      
      <div className="sb-grid-item">
        <h4 className="sb-section-item-heading">Fully Tested</h4>
        <p className="sb-section-item-paragraph">Comprehensive test coverage with Vitest and Testing Library. Every component is tested for functionality, accessibility, and edge cases.</p>
        <a href="https://github.com/storeware/polaris/tree/main/src/components" target="_blank">
          View tests →
        </a>
      </div>
      
      <div className="sb-grid-item">
        <h4 className="sb-section-item-heading">Accessibility First</h4>
        <p className="sb-section-item-paragraph">WCAG compliant components with proper ARIA support, keyboard navigation, and screen reader compatibility built-in.</p>
        <a href="https://polaris.shopify.com/foundations/accessibility" target="_blank">
          Accessibility guide →
        </a>
      </div>
      
      <div className="sb-grid-item">
        <h4 className="sb-section-item-heading">Optimized Bundle</h4>
        <p className="sb-section-item-paragraph">Tree-shakeable components with optimized bundle size. Import only what you need for better performance.</p>
        <a href="https://github.com/storeware/polaris#usage" target="_blank">
          Usage examples →
        </a>
      </div>
      
      <div className="sb-grid-item">
        <h4 className="sb-section-item-heading">shadcn/ui Foundation</h4>
        <p className="sb-section-item-paragraph">Built on proven shadcn/ui patterns for reliability, maintainability, and modern React best practices.</p>
        <a href="https://ui.shadcn.com/" target="_blank">
          shadcn/ui docs →
        </a>
      </div>
    </div>
  </div>
</div>

<div className="sb-addon">
  <div className="sb-addon-text">
    <h4>Quick Start</h4>
    <p className="sb-section-item-paragraph">
      Install the package and start building with Polaris-compatible components
      in minutes.
    </p>
    <code
      style={{
        background: "#f5f5f5",
        padding: "4px 8px",
        borderRadius: "4px",
        fontSize: "14px",
        display: "block",
        marginTop: "8px",
      }}
    >
      pnpm install @storeware/polaris
    </code>
  </div>
</div>

<div className="sb-section sb-socials">
  <div className="sb-section-item">
    <h4>🌟 GitHub</h4>
    <p>Contribute to the future of Polaris-compatible components.</p>
    <a href="https://github.com/storeware/polaris" target="_blank">
      Star on GitHub →
    </a>
  </div>

{" "}

<div className="sb-section-item">
  <h4>📚 Documentation</h4>
  <p>Read comprehensive documentation and component guides.</p>
  <a
    href="https://github.com/storeware/polaris/blob/main/STOREWARE_POLARIS_PRD.md"
    target="_blank"
  >
    View documentation →
  </a>
</div>

{" "}

<div className="sb-section-item">
  <h4>🎯 Examples</h4>
  <p>Explore component examples and implementation patterns.</p>
  <a href="?path=/story/components-button--primary" target="_blank">
    View examples →
  </a>
</div>

  <div className="sb-section-item">
    <h4>🎨 Polaris Design System</h4>
    <p>Learn about the original Polaris design system and principles.</p>
    <a href="https://polaris.shopify.com/" target="_blank">
      Polaris Design System →
    </a>
  </div>
</div>

<style>
  {`
  .sb-container {
    margin-bottom: 48px;
  }

  .sb-section {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 20px;
  }

  .sb-section-title {
    margin-bottom: 32px;
  }

  .sb-section a:not(h1 a, h2 a, h3 a) {
    font-size: 14px;
    text-decoration: none;
    color: #0066cc;
  }

  .sb-section a:hover {
    text-decoration: underline;
  }

  .sb-section-item, .sb-grid-item {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .sb-section-item-heading {
    padding-top: 20px !important;
    padding-bottom: 5px !important;
    margin: 0 !important;
  }
  
  .sb-section-item-paragraph {
    margin: 0;
    padding-bottom: 10px;
  }

  .sb-features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 32px 20px;
  }

  .sb-socials {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }

  .sb-socials p {
    margin-bottom: 10px;
  }

  .sb-addon {
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    background-color: #EEF3F8;
    border-radius: 5px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    height: 180px;
    margin-bottom: 48px;
    padding: 48px;
  }

  .sb-addon-text {
    max-width: 100%;
  }

  .sb-addon-text h4 {
    padding-top: 0px;
    margin-top: 0;
  }

  @media screen and (max-width: 600px) {
    .sb-section {
      flex-direction: column;
    }

    .sb-features-grid {
      grid-template-columns: repeat(1, 1fr);
    }

    .sb-socials {
      grid-template-columns: repeat(2, 1fr);
    }

    .sb-addon {
      height: auto;
      padding: 24px;
    }
  }
  `}
</style>
