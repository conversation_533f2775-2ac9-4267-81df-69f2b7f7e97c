# More info around this file at https://www.git-town.com/configuration-file

[branches]
main = "main"
perennials = []
perennial-regex = ""

[create]
new-branch-type = ""
share-new-branches = "no"

[hosting]
dev-remote = "origin"
forge-type = "github"
# origin-hostname = ""

[ship]
delete-tracking-branch = false
strategy = "api"

[sync]
feature-strategy = "merge"
perennial-strategy = "rebase"
prototype-strategy = "merge"
push-hook = true
tags = true
upstream = true
